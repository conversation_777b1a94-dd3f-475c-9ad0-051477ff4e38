import React, { useEffect, useMemo } from 'react'
import { InputAdornment } from '@mui/material'
import styles from './CommercialFields.module.scss'
import Loader from '@/src/component/shared/loader'
import NumberInputField from '@/src/component/shared/numberInputField'
import Tooltip from '@/src/component/shared/observerTooltip/ScrollHideTooltip'
import TextInputField from '@/src/component/shared/textInputField'
import AedIcon from '@/src/component/svgImages/aedIcon'
import usePhase from '@/src/redux/phase/usePhase'
import { populateDropdownOptions } from '@/src/utils/arrayUtils'
import { formatNumberWithCommas } from '@/src/utils/numberUtils'

const NUMBER_INPUT_STYLE = {
  '& .Mui-focused': {
    background: '#f4f4fc',
    '& .MuiOutlinedInput-notchedOutline': {
      // border: '1px solid #2333C2BF',
    },
  },
}
interface CommercialFieldsProps {
  formik: any
  isEditMode?: boolean
  loader?: boolean
}

const CommercialFields: React.FC<CommercialFieldsProps> = ({ formik, isEditMode, loader }) => {
  // const { phases, getMasterPhaseApi } = usePhase()

  // useEffect(() => {
  //   getMasterPhaseApi()
  // }, [])

  // const projectSubStageStatusesOptions = useMemo(() => {
  //   return populateDropdownOptions(phases, 'project_phase')
  // }, [phases])

  const { values } = formik

  return (
    <div className={styles.commercialFields}>
      {loader ? (
        <Loader />
      ) : (
        <>
          {/* <div className={styles.textAreaWrapper}>
            <Tooltip title={values.description} arrow>
              <div>
                <TextInputField
                  name="description"
                  labelText="Description"
                  // placeholder="Type something ..."
                  value={values.description}
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  fullWidth
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  variant={'outlined'}
                  disabled={!isEditMode}
                />
              </div>
            </Tooltip>
          </div> */}
          <div className={styles.content}>
            {/* Description */}
            {/* <TextInputField
          name="description"
          labelText="Description"
          placeholder="Type something ..."
          value={values.description}
          className={styles.textArea}
          fullWidth
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          variant={"outlined"}
        /> */}
            {/* <div></div>
        <div></div>
        <div></div>
        <div></div> */}
            {/* budget */}
            <Tooltip
              title={values?.budget ? (formatNumberWithCommas(values?.budget?.toLocaleString()) as string) : ''}
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="budget"
                  labelText="Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={values?.budget ? (formatNumberWithCommas(values?.budget?.toLocaleString()) as string) : ''}
                  format="comma-separated"
                  onChange={(value) => {
                    // formik.setFieldValue('budget', value)
                    formik.setFieldValue('budget', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.budget && Boolean(formik.errors.budget)}
                  helperText={formik.touched.budget && formik.errors.budget}
                />
              </div>
            </Tooltip>
            {/* Design Budget */}
            <Tooltip
              title={
                values?.design_budget ? (formatNumberWithCommas(values?.design_budget?.toLocaleString()) as string) : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="design_budget"
                  labelText="Design Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.design_budget
                      ? (formatNumberWithCommas(values?.design_budget?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('design_budget', value)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.design_budget && Boolean(formik.errors.design_budget)}
                  helperText={formik.touched.design_budget && formik.errors.design_budget}
                />
              </div>
            </Tooltip>

            {/* Supervision Budget */}
            <Tooltip
              title={
                values?.supervision_budget
                  ? (formatNumberWithCommas(values?.supervision_budget?.toLocaleString()) as string)
                  : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="supervision_budget"
                  labelText="Supervision Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.supervision_budget
                      ? (formatNumberWithCommas(values?.supervision_budget?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('supervision_budget', value)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.supervision_budget && Boolean(formik.errors.supervision_budget)}
                  helperText={formik.touched.supervision_budget && formik.errors.supervision_budget}
                />
              </div>
            </Tooltip>

            {/* Construction Budget */}
            <Tooltip
              title={
                values?.construction_budget
                  ? (formatNumberWithCommas(values?.construction_budget?.toLocaleString()) as string)
                  : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="construction_budget"
                  labelText="Construction Budget"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.construction_budget
                      ? (formatNumberWithCommas(values?.construction_budget?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('construction_budget', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.construction_budget && Boolean(formik.errors.construction_budget)}
                  helperText={formik.touched.construction_budget && formik.errors.construction_budget}
                />
              </div>
            </Tooltip>

            {/* contingency */}
            <Tooltip
              title={
                values?.contingency ? (formatNumberWithCommas(values?.contingency?.toLocaleString()) as string) : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="contingency"
                  labelText="Contingency"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.contingency ? (formatNumberWithCommas(values?.contingency?.toLocaleString()) as string) : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('contingency', value)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.contingency && Boolean(formik.errors.contingency)}
                  helperText={formik.touched.contingency && formik.errors.contingency}
                />
              </div>
            </Tooltip>

            {/* management_fees */}
            <Tooltip
              title={
                values?.management_fees
                  ? (formatNumberWithCommas(values?.management_fees?.toLocaleString()) as string)
                  : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="management_fees"
                  labelText="Management Fees"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.management_fees
                      ? (formatNumberWithCommas(values?.management_fees?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('management_fees', value)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.management_fees && Boolean(formik.errors.management_fees)}
                  helperText={formik.touched.management_fees && formik.errors.management_fees}
                />
              </div>
            </Tooltip>
            <Tooltip
              title={
                values?.committed_cost
                  ? (formatNumberWithCommas(values?.committed_cost?.toLocaleString()) as string)
                  : ''
              }
              arrow
            >
              <div>
                {/* Committed Cost */}
                <NumberInputField
                  isUpAndDowns={false}
                  name="committed_cost"
                  labelText="Committed Cost"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.committed_cost
                      ? (formatNumberWithCommas(values?.committed_cost?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('committed_cost', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.committed_cost && Boolean(formik.errors.committed_cost)}
                  helperText={formik.touched.committed_cost && formik.errors.committed_cost}
                />
              </div>
            </Tooltip>
            <Tooltip
              title={values?.vowd ? (formatNumberWithCommas(values?.vowd?.toLocaleString()) as string) : ''}
              arrow
            >
              <div>
                {/* vowd */}
                <NumberInputField
                  name="vowd"
                  isUpAndDowns={false}
                  labelText="Cumulative VOWD"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={values?.vowd ? (formatNumberWithCommas(values?.vowd?.toLocaleString()) as string) : ''}
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('vowd', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.vowd && Boolean(formik.errors.vowd)}
                  helperText={formik.touched.vowd && formik.errors.vowd}
                />
              </div>
            </Tooltip>

            {/* Forecast At Completion */}
            {/* <NumberInputField
          isUpAndDowns={false}
          name="forecast_at_completion"
          labelText="Forecast At Completion"
          placeholder="1,2,3..."
          value={
            values?.forecast_at_completion
              ? formatNumberWithCommas(
                  values?.forecast_at_completion?.toLocaleString()
                )
              : ""
          }
          format="comma-separated"
          onChange={(value) => {
            formik.setFieldValue("forecast_at_completion", parseInt(String(value)) || 0);
          }}
          onBlur={formik.handleBlur}
          sx={NUMBER_INPUT_STYLE}
          endAdornment={
            <InputAdornment position="start" className={styles.endAdornment}>
              <AedIcon className={styles.endAdornmentIcon} />
            </InputAdornment>
          }
        /> */}

            {/* year_planned_vowd */}
            <Tooltip
              title={
                values?.year_planned_vowd
                  ? (formatNumberWithCommas(values?.year_planned_vowd?.toLocaleString()) as string)
                  : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="year_planned_vowd"
                  labelText="Year Planned VOWD"
                  // placeholder="1,2,3..."
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.year_planned_vowd
                      ? (formatNumberWithCommas(values?.year_planned_vowd?.toLocaleString()) as string)
                      : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('year_planned_vowd', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.year_planned_vowd && Boolean(formik.errors.year_planned_vowd)}
                  helperText={formik.touched.year_planned_vowd && formik.errors.year_planned_vowd}
                />
              </div>
            </Tooltip>

            {/* paid_amount */}
            <Tooltip
              title={
                values?.paid_amount ? (formatNumberWithCommas(values?.paid_amount?.toLocaleString()) as string) : ''
              }
              arrow
            >
              <div>
                <NumberInputField
                  isUpAndDowns={false}
                  name="paid_amount"
                  labelText="Paid Amount"
                  className={`${!isEditMode ? styles.inputFields : styles.highlightField} `}
                  value={
                    values?.paid_amount ? (formatNumberWithCommas(values?.paid_amount?.toLocaleString()) as string) : ''
                  }
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('paid_amount', parseFloat(String(value)) || 0)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  maxLength={13}
                  disabled={!isEditMode}
                  error={formik.touched.paid_amount && Boolean(formik.errors.paid_amount)}
                  helperText={formik.touched.paid_amount && formik.errors.paid_amount}
                />
              </div>
            </Tooltip>

            {/* PVR */}
            <Tooltip title={'Total PVR of all Contracts & VOs PVR value'} arrow>
              <div>
                {/* AUTO-COMPLETE FIELD */}
                <NumberInputField
                  isUpAndDowns={false}
                  name="totalPVR"
                  labelText="PVR"
                  className={styles.inputFields}
                  value={values?.totalPVR ? (formatNumberWithCommas(values?.totalPVR?.toLocaleString()) as string) : ''}
                  format="comma-separated"
                  onChange={(value) => {
                    formik.setFieldValue('totalPVR', value)
                  }}
                  onBlur={formik.handleBlur}
                  sx={NUMBER_INPUT_STYLE}
                  endAdornment={
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <AedIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  }
                  disabled={true}
                />
              </div>
            </Tooltip>

            {/* Year Forecast VOWD */}
            {/* <NumberInputField
          isUpAndDowns={false}
          name="year_forecast_vowd"
          labelText="Year Forecast VOWD"
          placeholder="1,2,3..."
          className={styles.inputFields}
          value={
            values?.year_forecast_vowd
              ? formatNumberWithCommas(
                  values?.year_forecast_vowd?.toLocaleString()
                )
              : ""
          }
          format="comma-separated"
          onChange={(value) => {
            formik.setFieldValue("year_forecast_vowd", parseFloat(String(value)) || 0);
          }}
          onBlur={formik.handleBlur}
          sx={NUMBER_INPUT_STYLE}
          endAdornment={
            <InputAdornment position="start" className={styles.endAdornment}>
              <AedIcon className={styles.endAdornmentIcon} />
            </InputAdornment>
          }
          disabled={!isEditMode}
        /> */}
          </div>
        </>
      )}
    </div>
  )
}

export default CommercialFields

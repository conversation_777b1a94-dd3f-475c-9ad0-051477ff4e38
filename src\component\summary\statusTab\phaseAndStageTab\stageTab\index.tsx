import React, { useEffect, useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import { checkValidationStatus } from './helper'
import styles from './StageTab.module.scss'
import { deleteStatusRecord, dragAndDrop } from '../helper'
import { IStageFormTable, IStageTab } from '../interface'
import StageForm from '../stageForm'
import { filterMatchingRecordByKey, numberWithPrecision } from '../validationError/weightageValidation'
import ConfirmDeleteModal from '@/src/component/confirmDeleteModal'
import DragCell from '@/src/component/customCells/dragCell'
import Loader from '@/src/component/shared/loader'
import CommonPopoverForDisplayMultiSelect from '@/src/component/shared/popover/commonPopoverForDisplayMultiSelect'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { multiValueSorting, numericValueSorting } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import { showCustomToast } from '@/src/component/toast/ToastManager'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import { multiSelectOption } from '@/src/constant/enum'
import { MULTI_SELECT_SEPARATOR, PREDECESSOR_SUCCESSOR_SEPARATOR } from '@/src/constant/stageStatus'
import { useGetProjectToProjectPhaseCategories } from '@/src/hooks/useMasterProjectToProjectPhaseCategory'
import { useGetProjectPhaseCategories } from '@/src/hooks/useProjectPhaseCategory'
import { useGetProjectStageStatuses } from '@/src/hooks/useProjectStageStatus'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useCategory from '@/src/redux/category/useCategory'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import useProjectManagement from '@/src/redux/projectManagement/useProjectManagement'
import { ILookupProjectStatusPredecessor, ILookupProjectStatusSuccessor } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import {
  convertMultiSelectOption,
  flattenMixedArray,
  getMultiSelectedValue,
  getUniqueValuesFromArray,
  populateDropdownOptions,
  prepareDropdownOptions,
  prepareMultiPhaseCategoryDropdownOptions,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'
import { errorToast } from '@/src/utils/toastUtils'

const StageTab: React.FC<IStageTab> = ({
  gridFilters,
  setGridFilters,
  handleScrollToTop,
  selectTab,
  isStageVisited,
  setIsStageVisited,
}) => {
  const router = useRouter()
  const projectName = router.query.slug as string
  const { currentUser } = useAuthorization()
  const { localCategory } = useCategory()
  const [statusData, setStatusData] = useState([])
  const [filters, setFilters] = useState<any>({
    stageStatus: [],
    subStageStatus: [],
  })
  const [editData, setEditData] = useState<number | null>(null)
  const [deleteModel, setDeleteModel] = useState<any>(null)
  const { statuses, getStatusApi, deleteStatusApi, statusSortingApi, updateTableStatusApi } = useStatus()
  const { projectStageStatuses } = useGetProjectStageStatuses()
  const { projectPhaseCategories } = useGetProjectPhaseCategories()
  const { currentPeriod } = useMasterPeriod()
  const { projectManagements, deleteProjectManagementsApi } = useProjectManagement()
  const [loading, setLoading] = useState(false)
  const [edit, setEdit] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])
  const { uniquePhaseCategories } = usePhase()
  const { projectToProjectPhaseCategories } = useGetProjectToProjectPhaseCategories(currentPeriod, projectName)

  const projectCategoryOption: any = useMemo(() => {
    return prepareMultiPhaseCategoryDropdownOptions(
      projectToProjectPhaseCategories,
      'MasterProjectPhaseCategory',
      'project_phase_category',
    )
  }, [statuses])

  useEffect(() => {
    if (selectTab === 'Stage' && currentPeriod) {
      getStatusApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    }
  }, [selectTab, currentPeriod])

  const filteredStatuses = statuses.filter((item: any) => item.project_name === router.query.slug)

  useEffect(() => {
    const statusValue = filteredStatuses.map((data) => ({
      id: data.id,
      sortingOrder: Number(data.project_status_sorting_order),
      LookupProjectToProjectPhaseCategory: data?.LookupProjectToProjectPhaseCategory || [],
      LookupProjectToPhase: data?.LookupProjectToPhase || [],
      stageStatus: data?.MasterProjectStageStatus?.project_stage_status,
      subStage: data?.MasterProjectSubStage?.project_sub_stage,
      designStageWeightage: data.design_stage_weightage?.toString()
        ? (Number(data.design_stage_weightage) * 100).toFixed(2)
        : null,
      phaseWeightage: data.phase_weightage?.toString() ? (Number(data.phase_weightage) * 100).toFixed(2) : null,
      predecessor: data.LookupProjectStatusPredecessor || [],
      successor: data.LookupProjectStatusSuccessor || [],
      categoryIDsArray: data?.LookupProjectToProjectPhaseCategory
        ? data?.LookupProjectToProjectPhaseCategory.map((cat) => cat.id)
        : [],
      phaseIDsArray: data?.LookupProjectToPhase ? data?.LookupProjectToPhase.map((phase) => phase.id) : [],
      statusID: data?.MasterProjectStageStatus?.id?.toString() || '',
      successorIDs:
        data.LookupProjectStatusSuccessor?.map(
          (item: ILookupProjectStatusSuccessor) => item?.DestinationProjectStatus?.destination_project_status_id,
        ) || [],
      predecessorIDs:
        data.LookupProjectStatusPredecessor?.map(
          (item: ILookupProjectStatusPredecessor) => item?.DestinationProjectStatus?.destination_project_status_id,
        ) || [],
    }))

    const stageStatusMapping: any = {
      'Status-Initiation': 'Initiation',
      'Status-LDC Procurement': 'LDC Procurement',
      'Status-Design': 'Design',
      'Status-Contractor Procurement': 'Contractor Procurement',
      'Status-Construction': 'Construction',
      'Status-DLP and Project Closeout': 'DLP and Project Closeout',
    }

    const allowedStatuses = currentUser.role?.view_permissions.map((permission: any) => stageStatusMapping[permission])

    const filteredData = statusValue.filter((item) => allowedStatuses.includes(item.stageStatus))
    const sortData: any = sortArrayByKeyWithTypeConversion(filteredData, 'sortingOrder', true)

    setStatusData(sortData)

    const filterStageStatus = filteredData?.map((item) => {
      return item?.stageStatus
    })
    const filterStageSubStatus = filteredData?.map((item: any) => {
      return item?.subStage
    })
    const filterPhase = filteredData?.map((item: any) => {
      return item?.phase
    })
    const filterPredecessor = flattenMixedArray(
      filteredData?.map((item: any) => {
        return item?.predecessor
      }),
    )
    const filterSuccessor = flattenMixedArray(
      filteredData?.map((item: any) => {
        return item?.successor
      }),
    )

    setFilters({
      stageStatus: [multiSelectOption?.SELECT_ALL, ...new Set(filterStageStatus)],
      subStageStatus: [...new Set(filterStageSubStatus), multiSelectOption?.SELECT_ALL],
      phase: [multiSelectOption?.SELECT_ALL, ...new Set(filterPhase)],
      predecessor: [multiSelectOption?.SELECT_ALL, ...new Set(filterPredecessor)],
      successor: [multiSelectOption?.SELECT_ALL, ...new Set(filterSuccessor)],
    })
  }, [statuses, router.query.slug, currentUser.role?.view_permissions])

  const handleDelete = async (row: any) => {
    await deleteStatusRecord(
      row,
      projectManagements,
      currentPeriod,
      setDeleteModel,
      deleteProjectManagementsApi,
      deleteStatusApi,
      getStatusApi,
      setDeleteLoading,
    )
  }

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    await dragAndDrop(
      statuses,
      filteredStatuses,
      currentPeriod,
      dragId,
      dropId,
      setLoading,
      statusSortingApi,
      getStatusApi,
      router?.query?.slug,
    )
  }

  const statuesKey: any = {
    projectPhaseCategory: 'master_project_phase_category_id',
    phaseWeightage: 'phase_weightage',
    designStageWeightage: 'design_stage_weightage',
  }

  const phaseMappingWithCategory = (category: string, phase: string): string => {
    const selectedCategories = getMultiSelectedValue(category)?.filter((item: string) => item.length)
    const validPhases =
      uniquePhaseCategories
        ?.filter((res) => res?.phase && selectedCategories?.includes(res?.category))
        ?.map((res) => res?.phase) || []
    let selectedPhases: string[] = getMultiSelectedValue(phase)
    selectedPhases = selectedPhases?.filter((ph) => validPhases.includes(ph))
    return selectedPhases?.join(MULTI_SELECT_SEPARATOR)
  }

  const onCellUpdate = async (cell: any, newValue: any, row: any) => {
    try {
      const { id, ...rowData } = row
      const findStageStatus = filteredStatuses.find((item: any) => item.id === id)
      rowData[cell.columnId] = newValue
      const keyName = statuesKey[cell.columnId]
      const commonPayload = {
        period: currentPeriod,
        project_name: router.query.slug,
        last_updated: new Date().toISOString(),
      }

      //TODO : Update Phase Mapping
      // let selectedPhaseString = row?.phase
      // if (cell.columnId === 'projectPhaseCategory') {
      //   selectedPhaseString = row?.phase ? phaseMappingWithCategory(newValue, row?.phase) : null
      // }

      const payload: any = {
        project_to_project_phase_category_ids:
          cell.columnId === 'projectPhaseCategory' ? newValue : row?.categoryIDsArray || null,
        project_to_project_phase_ids: row?.phaseIDsArray || null,
        master_project_stage_status_id: Number(row?.statusID),
        phase_weightage: cell.columnId === 'phaseWeightage' ? newValue : row?.phaseWeightage,
        design_stage_weightage: cell.columnId === 'designStageWeightage' ? newValue : row?.designStageWeightage,
        predecessor_ids: row?.predecessorIDs,
        successor_ids: row?.successorIDs,
        master_project_sub_stage_id: row?.subStage || null,
      }

      const currentStatus: any = statuses.find((item: any) => item.id.toString() === row?.id.toString())

      const validationMessages = await checkValidationStatus(payload, currentStatus, statusData)
      if (validationMessages.length > 0) {
        setValidationMessage(validationMessages)
        setIsValidationModel(true)
        return
      }

      if ('phase_weightage' in payload) {
        payload.phase_weightage = payload.phase_weightage?.toString()
          ? numberWithPrecision(payload.phase_weightage)
          : null
      }

      if ('design_stage_weightage' in payload) {
        payload.design_stage_weightage = payload.design_stage_weightage?.toString()
          ? numberWithPrecision(payload.design_stage_weightage)
          : null
      }

      // Update the data
      const response: any = await updateTableStatusApi({ id: id as number, data: { ...payload, ...commonPayload } })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message)
        return true
      }

      // Fetch updated data
      const getData: Record<string, any> = await getStatusApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
      return getData?.payload?.success ?? false
    } catch (error) {
      console.error('Error while updating cell of key achievement:', error)
      return false
    } finally {
      !isStageVisited && setIsStageVisited && setIsStageVisited(true)
    }
  }

  const projectPhaseCategoryOption: any = useMemo(
    () => prepareDropdownOptions(projectPhaseCategories, 'project_phase_category'),
    [projectStageStatuses],
  )

  // const projectCategoryOption: any = useMemo(() => {
  //   const optionMap = new Map()
  //   statuses.forEach((item: any) => {
  //     if (item.project_phase_category?.includes(MULTI_SELECT_SEPARATOR)) {
  //       const multiCategories = item.project_phase_category.split(MULTI_SELECT_SEPARATOR)
  //       multiCategories.forEach((category: any) => {
  //         optionMap.set(category, { id: category, name: category })
  //       })
  //     } else {
  //       optionMap.set(item.project_phase_category, {
  //         id: item.project_phase_category,
  //         name: item.project_phase_category,
  //       })
  //     }
  //   })
  //   localCategory.forEach((item: any) => {
  //     if (item.category?.includes(MULTI_SELECT_SEPARATOR)) {
  //       const multiCategories = item.category.split(MULTI_SELECT_SEPARATOR)
  //       multiCategories.forEach((category: any) => {
  //         optionMap.set(category, { id: category, name: category })
  //       })
  //     } else {
  //       optionMap.set(item.category, { id: item.category, name: item.category })
  //     }
  //   })
  //   let projectCategoryOption = Array.from(optionMap.values()).filter((item: any) => item.name?.length)

  //   return projectCategoryOption?.map((item: any) => item.id)
  // }, [statuses, localCategory])

  const checkAllPhaseNullOrEmpty = (array: any) => {
    if (array.length === 0) return true
    // Check if every object in the array has a phase value of null
    return array.every((obj: any) => obj.phase === null)
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'actionCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'center',
    },
    {
      accessorKey: 'action',
      header: 'Action',
      cell: ({ row }) => (
        <div className={styles.actionBts}>
          <EditIcon
            onClick={() => {
              handleScrollToTop()
              setEditData(Number(row.id))
            }}
          />
          <DeleteIcon
            className={styles.deleteRowIcon}
            onClick={() => setDeleteModel({ ...row, project_name: router?.query?.slug as string })}
          />
        </div>
      ),
      size: 70,
    },
    { accessorKey: 'sortingOrder', header: 'ID', size: 60, visible: false },
    {
      accessorKey: 'projectPhaseCategory',
      header: 'Project Phase Category',
      // size: 200,
      flex: 1,
      isEditableCell: edit,
      // align: 'center',
      filterType: 'wildcard-multi-select',
      editableType: 'multiDropDown',
      editOption: convertMultiSelectOption(projectCategoryOption).filter(
        (item: any) => item.id !== null && item.name !== null,
      ),
      customEditValue: (cell: any, row: any) => {
        return row?.categoryIDsArray || []
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      sortingFn: (...data) => multiValueSorting(...data, 'Multi categories'),
      cell: ({ row }) => (
        <>
          {row.original.LookupProjectToProjectPhaseCategory?.length > 1 ? (
            <div>
              <span className={styles.infoContainer}>
                <span>Multi categories</span>
                <span className={styles.infoIcon} aria-describedby="multi-value-popover">
                  <CommonPopoverForDisplayMultiSelect
                    content={prepareMultiPhaseCategoryDropdownOptions(
                      row.original.LookupProjectToProjectPhaseCategory,
                      'MasterProjectPhaseCategory',
                      'project_phase_category',
                    )}
                    maxWidth={350}
                    placement="right"
                  />
                </span>
              </span>
            </div>
          ) : (
            <>
              {row.original.LookupProjectToPhase?.length === 0 ? (
                <div>{'-'}</div>
              ) : (
                prepareMultiPhaseCategoryDropdownOptions(
                  row.original.LookupProjectToProjectPhaseCategory,
                  'MasterProjectPhaseCategory',
                  'project_phase_category',
                )?.map((item) => item.label)
              )}
            </>
          )}
        </>
      ),
      isSaveConfirmationRequired: (_cell: any, val: any) => {
        return val ? true : false
      },
      validationMessage: (cell, newValue) => {
        const oldPhaseInArray = cell?.row?.original?.phase ? getMultiSelectedValue(cell?.row?.original?.phase) : []
        const newPhase = cell?.row?.original?.phase
          ? phaseMappingWithCategory(newValue, cell?.row?.original?.phase)
          : null
        const newPhaseInArray = newPhase ? getMultiSelectedValue(newPhase) : []
        const removedItems = oldPhaseInArray.filter((item) => !newPhaseInArray.includes(item))
        const isPhaseChanged = removedItems.length > 0
        return isPhaseChanged
          ? `Removing the category will also remove it's associated phase (${removedItems.join(', ')}). Do you want to proceed?`
          : ''
      },
    },
    {
      accessorKey: 'phase',
      header: 'Phase/Package',
      // size: 150,
      flex: 1,
      filterType: 'phase/package',
      listOption: convertMultiSelectOption(filters.phase),
      sortingFn: (...data) => multiValueSorting(...data, 'Multi phases'),
      cell: ({ row }) => (
        <>
          {row.original.LookupProjectToPhase?.length > 1 ? (
            <div>
              <span className={styles.infoContainer}>
                <span>Multi phases</span>
                <span className={styles.infoIcon} aria-describedby="multi-value-popover">
                  <CommonPopoverForDisplayMultiSelect
                    // title="Multi categories"
                    content={prepareDropdownOptions(row.original.LookupProjectToPhase, 'phase')}
                    maxWidth={350}
                    placement="right"
                  />
                </span>
              </span>
            </div>
          ) : (
            (
              <>
                {checkAllPhaseNullOrEmpty(row?.original?.LookupProjectToPhase) ? (
                  <div>{'-'}</div>
                ) : (
                  prepareDropdownOptions(row.original.LookupProjectToPhase, 'phase')?.map((item) => item.label)
                )}
              </>
            ) || '-'
          )}
        </>
      ),
    },
    {
      accessorKey: 'stageStatus',
      header: 'Stage Status',
      filterType: 'projectStatus',
      filterWithRelation: [{ row: 'subStage', value: 'design' }],
      filterRow: ['stageStatus', 'subStage'],
      listOption: convertMultiSelectOption(filters.stageStatus, 'Design', filters.subStageStatus),
      // size: 150,
      flex: 1,
      cell: ({ row }) => (
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span>{row.original.stageStatus}</span>
          <span className={styles.subStatus}>{row.original.subStage}</span>
        </div>
      ),
    },
    {
      accessorKey: 'phaseWeightage',
      header: 'Phase Weightage',
      // size: 200,
      flex: 1,
      isEditableCell: edit,
      editableType: 'number',
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      isSaveConfirmationRequired: true,
      validationMessage: (cell, newValue) => {
        const match = filterMatchingRecordByKey(
          statusData,
          {
            projectPhaseCategory: cell?.row?.original?.projectPhaseCategory,
            phase: cell?.row?.original?.phase,
            stageStatus: cell?.row?.original?.stageStatus,
          },
          MULTI_SELECT_SEPARATOR,
        )
        const projectPhaseCategory = cell?.row?.original?.projectPhaseCategory
          ?.split(MULTI_SELECT_SEPARATOR)
          ?.join(', ')
        const phase = cell?.row?.original?.phase
          ? cell?.row?.original?.phase?.split(MULTI_SELECT_SEPARATOR)?.join(', ')
          : '-'
        const stageStatus = cell?.row?.original?.stageStatus
        return stageStatus === 'Design' && match?.length > 1
          ? `This action will reset all the phase weightages of the selected combination of (${projectPhaseCategory} / ${phase} / ${stageStatus}). Do you want proceed?`
          : ''
      },
      cell: ({ row }) => {
        return <span>{row.original.phaseWeightage || '-'}</span>
      },
    },
    {
      accessorKey: 'designStageWeightage',
      header: 'Design Stage Weightage',
      // size: 200,
      filterType: 'number',
      sortingFn: (...rest) => numericValueSorting(...rest),
      flex: 1,
      isEditableCell: (cell: any) => {
        const stageStatus = cell?.row?.original?.stageStatus
        return stageStatus === 'Design' && edit
      },
      editableType: 'number',
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      isSaveConfirmationRequired: true,
      cell: ({ row }) => {
        return <span>{row.original.designStageWeightage || '-'}</span>
      },
    },
    {
      accessorKey: 'predecessor',
      header: 'Predecessor',
      filterType: 'wildcard-predecessor-successor',
      listOption: convertMultiSelectOption(filters.predecessor),
      cell: ({ row }) => {
        const predecessorData = row.original.predecessor.map((item: any) => item.DestinationProjectStatus)
        const predecessor: string[] = []
        predecessorData.forEach((item: any) => {
          const categories =
            item?.LookupProjectToProjectPhaseCategory?.map(
              (category: any) => category?.MasterProjectPhaseCategory?.project_phase_category,
            ).join(', ') || null
          const phase = item?.LookupProjectToPhase?.map((phase: any) => phase?.phase).join(', ') || null
          const stage = item?.stage_status || null
          const subStage = item?.sub_stage || null
          const predecessorString = `${categories ? `${categories} / ` : ''}${phase ? `${phase} / ` : ''}${stage ? `${stage}` : ''}${subStage ? ` / ${subStage}` : ''}`
          predecessor.push(predecessorString)
        })
        return (
          <>
            {predecessor?.length > 0 ? (
              predecessor?.length > 1 ? (
                <div>
                  <span className={styles.infoContainer}>
                    <span>Multi predecessors</span>
                    <span className={styles.infoIcon} aria-describedby="multi-value-popover">
                      <CommonPopoverForDisplayMultiSelect
                        // title="Multi categories"
                        content={predecessor.map((item: string) => ({
                          label: item,
                          value: item,
                        }))}
                        maxWidth={350}
                        placement="right"
                      />
                    </span>
                  </span>
                </div>
              ) : (
                predecessor[0]
              )
            ) : (
              <div> - </div>
            )}
          </>
        )
      },
      size: 220,
    },
    {
      accessorKey: 'successor',
      header: 'Successor',
      filterType: 'wildcard-predecessor-successor',
      listOption: convertMultiSelectOption(filters.successor),
      cell: ({ row }) => {
        const successorData = row.original.successor?.map((item: any) => item.DestinationProjectStatus)
        const successor: string[] = []
        successorData.forEach((item: any) => {
          const categories =
            item?.LookupProjectToProjectPhaseCategory?.map(
              (category: any) => category?.MasterProjectPhaseCategory?.project_phase_category,
            ).join(', ') || null
          const phase = item?.LookupProjectToPhase?.map((phase: any) => phase?.phase).join(', ') || null
          const stage = item?.stage_status || null
          const subStage = item?.sub_stage || null
          const successorString = `${categories ? `${categories} / ` : ''}${phase ? `${phase} / ` : ''}${stage ? `${stage}` : ''}${subStage ? ` / ${subStage}` : ''}`
          successor.push(successorString)
        })
        return (
          <>
            {successor?.length > 0 ? (
              successor?.length > 1 ? (
                <div>
                  <span className={styles.infoContainer}>
                    <span>Multi successors</span>
                    <span className={styles.infoIcon} aria-describedby="multi-value-popover">
                      <CommonPopoverForDisplayMultiSelect
                        // title="Multi categories"
                        content={successor.map((item: string) => ({
                          label: item,
                          value: item,
                        }))}
                        maxWidth={350}
                        placement="right"
                      />
                    </span>
                  </span>
                </div>
              ) : (
                successor[0]
              )
            ) : (
              <div> - </div>
            )}
          </>
        )

        // return (<>
        //   <Tooltip title={toolTipMessage} style={{ fontSize: '12px' }} arrow>
        //     {row.original.successor?.length > 0 && row.original.successor[0]?.split(PREDECESSOR_SUCCESSOR_SEPARATOR).length > 1 ? (
        //       <div>
        //         <span className={styles.infoContainer}>
        //           <span>Multi successors</span>
        //           <span
        //             className={styles.infoIcon}
        //             aria-describedby="multi-value-popover"
        //           >
        //             <Image src={InfoIcon} alt="info" width={16} height={16} />
        //           </span>
        //         </span>
        //       </div>
        //     ) : (
        //       row.original.successor[0]?.split(PREDECESSOR_SUCCESSOR_SEPARATOR)?.join(' | ')?.split(MULTI_SELECT_SEPARATOR)?.join(', ')
        //     )}
        //   </Tooltip>
        // </>)
      },
      size: 220,
    },
  ]

  return (
    <div className={styles.container}>
      {loading ? (
        <div className={styles.loader}>
          <Loader />
        </div>
      ) : (
        <>
          <div className={styles.header}>
            <div className={styles.leftButtons}></div>
            <div className={styles.actionButtons}></div>
          </div>
          <div className={styles.content}>
            <div className={styles.contentHeader}>
              <div className={styles.leftContent}>
                <span className={styles.projectName}>{router.query.slug}</span>
              </div>
              <div className={styles.actionStagesButtons}></div>
            </div>
            <StageForm
              editData={editData}
              setEditData={setEditData}
              statusData={statusData}
              setLoading={setLoading}
              edit={edit}
              setEdit={setEdit}
              setIsStageVisited={setIsStageVisited}
              isStageVisited={isStageVisited}
              gridFilters={gridFilters}
              setGridFilters={setGridFilters}
            />
          </div>
          <div>
            {statusData.length ? (
              <div className={styles.tableWrapper}>
                <TanStackTable
                  rows={statusData}
                  columns={columns}
                  onDragEnd={handleDragAndDrop}
                  enableSticky={false}
                  gridFilters={gridFilters}
                  setGridFilters={setGridFilters}
                />
              </div>
            ) : (
              ''
            )}
          </div>
        </>
      )}
      <ConfirmDeleteModal
        open={Boolean(deleteModel)}
        onClose={() => setDeleteModel(null)}
        handleConfirm={() => handleDelete(deleteModel)}
        loading={deleteLoading}
      />
      <PulseModel
        closable={false}
        style={{ width: 'fitContent', minWidth: 450 }}
        open={isValidationModel}
        onClose={() => setIsValidationModel(false)}
        content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
      />
    </div>
  )
}

export default StageTab

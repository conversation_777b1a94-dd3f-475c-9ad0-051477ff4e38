// generateOptionsHelper.ts

import { CustomColumnDef } from '../../shared/tanStackTable/interface'
import { MULTI_SELECT_SEPARATOR, PREDECESSOR_SUCCESSOR_SEPARATOR } from '@/src/constant/stageStatus'
import { ILookupProjectToPhase, ILookupProjectToProjectPhaseCategory } from '@/src/redux/status/interface'
import { sortPredecessorSuccessorFilterOption } from '@/src/utils/predecessorSuccessor'
import { naturalSort } from '@/src/utils/sortingUtils'

// Helper function to reorder items based on predefined order
export const reorderArray = (inputArray: any[], order: string[]) => {
  const orderedPart: any[] = []
  const unorderedPart: any[] = []

  inputArray.forEach((item) => {
    if (order.includes(item.value)) {
      orderedPart.push(item)
    } else {
      unorderedPart.push(item)
    }
  })

  orderedPart.sort((a, b) => order.indexOf(a.value) - order.indexOf(b.value))
  return [...orderedPart, ...unorderedPart]
}

// Helper function to generate options for `progress` filter type
export const generateProgressOptions = () => [
  { label: '0%-25%', value: '0%-25%' },
  { label: '25%-50%', value: '25%-50%' },
  { label: '50%-75%', value: '50%-75%' },
  { label: '75%-100%', value: '75%-100%' },
]

export const generateBooleanOptions = () => [
  { label: 'Check', value: true },
  { label: 'Blank', value: false },
]

export const predefinedProjectStatusOrder = [
  'Initiation',
  'LDC Procurement',
  'Design',
  'Contractor Procurement',
  'Construction',
  'DLP and Project Closeout',
  'Transfer In Progress',
]

// Helper function to generate options for `projectStatus` filter type
export const generateProjectStatusOptions = (rows: any[], accessorKey: string, subItemKey: string | null) => {
  return rows.reduce((acc: any[], item: any) => {
    const mainItemValue = item[accessorKey]
    if (!mainItemValue) return acc

    let mainItem = acc.find((entry: any) => entry.value === mainItemValue)
    if (!mainItem) {
      mainItem = { label: mainItemValue, value: mainItemValue, subItem: [] }
      acc.push(mainItem)
    }

    if (subItemKey) {
      const subItemValue = item[subItemKey]
      const normalizedSubItemValue = String(subItemValue).toLowerCase().trim()
      const isDuplicate = mainItem.subItem.some(
        (sub: any) => String(sub.value).toLowerCase().trim() === normalizedSubItemValue,
      )
      if (subItemValue && !isDuplicate) {
        mainItem.subItem.push({ label: subItemValue, value: subItemValue })
      }
    }

    return reorderArray(acc, predefinedProjectStatusOrder)
  }, [])
}

// Helper function to generate options for `PredecessorSuccessor` filter type
export const generatePredecessorSuccessorOptions = (rows: any[], accessorKey: string, subItemKey: string | null) => {
  return rows?.reduce((acc: any[], item: any) => {
    const mainItemValue = item[accessorKey]
    if (!mainItemValue) return acc

    const newObject = mainItemValue.map((pre: any) => {
      const projectPhaseCategories = pre?.DestinationProjectStatus?.LookupProjectToProjectPhaseCategory?.map(
        (item: ILookupProjectToProjectPhaseCategory) => item.MasterProjectPhaseCategory?.project_phase_category,
      )?.join(', ')
      const phase = pre?.DestinationProjectStatus?.LookupProjectToPhase.map(
        (item: ILookupProjectToPhase) => item.phase,
      )?.join(', ')
      const stageStatus = pre?.DestinationProjectStatus?.stage_status
      const subStage = pre?.DestinationProjectStatus?.sub_stage

      const name = `${projectPhaseCategories}${phase ? ` / ${phase}` : ''}${stageStatus ? ` / ${stageStatus}` : ''}${subStage ? ` / ${subStage}` : ''}`

      return { label: name?.trim(), value: pre?.project_status_id }
    })

    newObject.forEach((value: { label: string; value: number }) => {
      if (value?.label) {
        let mainItem = acc.find((entry: any) => entry.value === value?.label)
        if (!mainItem) {
          mainItem = { ...value, subItem: [] }
          acc.push(mainItem)
        }

        if (subItemKey) {
          const subItemValue = item[subItemKey]
          if (subItemValue && !mainItem.subItem.find((sub: any) => sub.value === subItemValue)) {
            mainItem.subItem.push({ label: subItemValue, value: subItemValue })
          }
        }
      }
    })

    return sortPredecessorSuccessorFilterOption(acc)
  }, [])
}

// Helper function to generate options for `multiSelect` filter type
export const generateMultiSelectOptions = (
  rows: any[],
  accessorKey: string,
  subItemKey: string | null,
  seperator = ',',
) => {
  return rows?.reduce((acc: any[], item: any) => {
    const mainItemValue = item[accessorKey]
    if (!mainItemValue) return acc

    const mainItemValues = mainItemValue?.split(seperator).map((val: string) => val.trim())

    mainItemValues.forEach((value: string) => {
      if (value) {
        let mainItem = acc.find((entry: any) => entry.value === value)
        if (!mainItem) {
          mainItem = { label: value, value: value, subItem: [] }
          acc.push(mainItem)
        }

        if (subItemKey) {
          const subItemValue = item[subItemKey]
          if (subItemValue && !mainItem.subItem.find((sub: any) => sub.value === subItemValue)) {
            mainItem.subItem.push({ label: subItemValue, value: subItemValue })
          }
        }
      }
    })

    return acc
  }, [])
}

// Default function to generate options when none of the other types match
export const generateDefaultOptions = (rows: any[], accessorKey: string, subItemKey: string | null) => {
  return rows?.reduce((acc: any[], item: any) => {
    const mainItemValue = item[accessorKey]
    // Change condition to check for undefined/null specifically
    if (mainItemValue === undefined || mainItemValue === null || !mainItemValue?.toString()) return acc

    let mainItem = acc?.find((entry: any) => entry.value === mainItemValue)
    if (!mainItem) {
      mainItem = { label: mainItemValue, value: mainItemValue, subItem: [] }
      // mainItem = { label: mainItemValue?.toString()?.trim(), value: mainItemValue?.toString()?.trim(), subItem: [] }
      acc.push(mainItem)
    }

    if (subItemKey) {
      const subItemValue = item[subItemKey]
      if ((subItemValue === 0 || subItemValue) && !mainItem?.subItem?.find((sub: any) => sub.value === subItemValue)) {
        mainItem?.subItem?.push({ label: subItemValue, value: subItemValue })
      }
    }

    return acc
  }, [])
}

// Main function to generate options based on filter type
export const generateOptions = (columnDef: CustomColumnDef<any>, rows: any[], subItemKey: string | null) => {
  const accessorKey = columnDef.accessorKey
  const sortAlphabetically = columnDef.sortAlphabetically
  const sortItemsAlphabetically = (items: any[]): any[] => {
    return [...items].sort((a, b) => naturalSort(a.label, b.label))
  }
  switch (columnDef?.filterType) {
    case 'progress':
      return generateProgressOptions()
    case 'boolean':
      return generateBooleanOptions()
    case 'projectStatus':
      return sortAlphabetically
        ? sortItemsAlphabetically(generateProjectStatusOptions(rows, accessorKey, subItemKey))
        : generateProjectStatusOptions(rows, accessorKey, subItemKey)
    case 'multiSelect':
      return sortAlphabetically
        ? sortItemsAlphabetically(generateMultiSelectOptions(rows, accessorKey, subItemKey))
        : generateMultiSelectOptions(rows, accessorKey, subItemKey)
    case 'wildcard-multi-select':
    case 'phase/package':
      return sortAlphabetically
        ? sortItemsAlphabetically(generateMultiSelectOptions(rows, accessorKey, subItemKey, MULTI_SELECT_SEPARATOR))
        : generateMultiSelectOptions(rows, accessorKey, subItemKey, MULTI_SELECT_SEPARATOR)
    case 'wildcard-predecessor-successor':
      return sortAlphabetically
        ? sortItemsAlphabetically(generatePredecessorSuccessorOptions(rows, accessorKey, subItemKey))
        : generatePredecessorSuccessorOptions(rows, accessorKey, subItemKey)
    default:
      return sortAlphabetically
        ? sortItemsAlphabetically(generateDefaultOptions(rows, accessorKey, subItemKey))
        : generateDefaultOptions(rows, accessorKey, subItemKey)
  }
}

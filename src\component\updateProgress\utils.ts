import { differenceInDays, parse } from 'date-fns'
import { projectManagementFields } from './constant'
import { slippageJustificationValidation } from '../summary/statusTab/statusService'
import { showCustomToast } from '../toast/ToastManager'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
} from '@/src/constant/stageStatus'
import { ILookupProjectToPhase, IStatus } from '@/src/redux/status/interface'
import { payloadDateFormate } from '@/src/utils/dateUtils'

export const handleProjectManagement = async (
  values: any,
  projectManagement: any[],
  status: any,
  currentPeriod: string,
  router: any,
  addProjectManagementsApi: any,
  updateProjectManagementsApi: any,
  getProjectManagementsApi: any,
) => {
  const payload: any = {}

  // Process direct fields
  projectManagementFields.forEach((field) => {
    if (field in values) {
      payload[field] =
        field === 'time_elapsed_percentage' || field === 'cost_per_sqm' ? values[field] || null : values[field] || null
    }
  })

  const existingEntry = projectManagement.find((item: any) => {
    return (
      status?.ProjectManagement?.id === item?.id
      //TODO: Old code commented, Now we manage ProjectManagement with progress-status API
      /*       status?.project_name === item?.project_name &&
      status?.LookupProjectToPhase?.some((itemPhase: ILookupProjectToPhase) =>
        item?.LookupProjectToPhase?.some((lookupPhase: ILookupProjectToPhase) => lookupPhase.id === itemPhase.id),
      ) &&
      item.MasterProjectStageStatus?.project_stage_status === status?.MasterProjectStageStatus?.project_stage_status &&
      (status?.MasterProjectStageStatus?.project_stage_status === 'Design'
        ? item.master_project_sub_stage_id === status?.MasterProjectSubStage?.id
        : true) */
    )
  })

  let hasChanges = false

  const logDifferences = () => {
    for (const key in payload) {
      const normalizedPayloadValue = payload[key]
      const normalizedExistingValue = existingEntry ? existingEntry[key] : null

      // Check value only, ignoring type
      if (normalizedPayloadValue != normalizedExistingValue) {
        // For debugging: log the key and values
        return true // Exit early if a difference is found
      }
    }
    return false
  }

  hasChanges = logDifferences()
  if (hasChanges) {
    payload['last_updated'] = new Date()
    payload['period'] = currentPeriod
    payload['project_name'] = status?.project_name ?? router.query.slug
    payload['project_status_id'] = status?.id

    //TODO: Now BE side manage with 'project_status_id' field, So not need commented fields.
    // payload['master_project_stage_status_id'] = status?.MasterProjectStageStatus?.id
    // payload['project_to_project_phase_ids'] = status?.LookupProjectToPhase?.map(
    //   (item: ILookupProjectToPhase) => item?.id,
    // )
    // payload['master_project_sub_stage_id'] = status?.MasterProjectSubStage?.id

    const response: Record<string, any> = existingEntry
      ? await updateProjectManagementsApi({ id: existingEntry.id as number, data: payload })
      : await addProjectManagementsApi(payload)

    if (response.payload.success) {
      const res: any = await getProjectManagementsApi({ period: currentPeriod, project_name: status?.project_name })
      if (res.payload.success) return res.payload.data
    } else {
      showCustomToast('Error', response?.payload?.response?.data?.message || 'error')
    }
  }
}

export const handleStatus = async (
  values: any,
  status: any,
  highlights: string[],
  currentPeriod: string,
  router: any,
  addStatusApi: any,
  updateStatusApi: any,
  formik: any,
  statuses: any,
  getStatusApi: any,
  updateMasterProjectApi: any,
  currentProject: any,
  isUpdateHighlight: boolean,
  setIsUpdateHighlight: (args: boolean) => void,
) => {
  const highlightValue = highlights.join(', ')
  const basicDetails: any = {}

  const isContractDlp = [CONTRACTOR_PROCUREMENT, DLP_PROJECT_CLOSEOUT]
  const isInitiationDesignLdc = [LDC_PROCUREMENT, DESIGN, INITIATION]

  // NOTE : duration_in_days is auto calculated field from backend
  const basicDetailsFields = [
    // 'supervision_consultant',
    'design_manager_ids',
    // 'contractor',
    // 'pmc_consultant',
    'project_to_project_phase_ids',
    'lookup_project_to_project_phase_category',
    'master_procurement_manager_id',
    // 'consultant',
    'master_supervision_consultant_id',
    'master_contractor_id',
    'master_consultant_id',
    'master_pmc_consultant_id',
    'plan_contractor_progress_percentage',
    // 'duration_in_days',
    'master_procurement_manager_id',
    'delivery_project_manager_ids',
    'is_performance_bond_received',
    'is_advance_payment_released',
    'is_insurances_received',
    'is_advance_payment_bond_received',
    'is_contract_signature_received',
    'pte',
    'pte_attachment',
    'plan_duration',
  ]

  const dateFields = [
    'contract_start_date',
    'contract_end_date',
    'eot_to_contractor',
    'procurement_start_date',
    'kickoff_meeting_date',
  ]
  const keyExists = (obj: object, key: string) => key in obj
  dateFields.forEach((field) => {
    if (field in values) {
      basicDetails[field] = values[field] ? payloadDateFormate(values[field]) : null
    }
  })

  const getDateDifference = (date1: string | Date, date2: string | Date): number => {
    const format = 'yyyy-MM-dd' // Define the date format

    const d1 = parse(String(date1), format, new Date())
    const d2 = parse(String(date2), format, new Date())

    return differenceInDays(d1, d2)
  }

  const calculateVariance = (planDateKey: string, contractDateKey: string) => {
    const planDate = keyExists(values, planDateKey) ? values[planDateKey] : status[planDateKey]
    const contractDate = keyExists(values, contractDateKey) ? values[contractDateKey] : status[contractDateKey]

    const formattedPlanDate = payloadDateFormate(planDate)
    const formattedContractDate = payloadDateFormate(contractDate)

    return formattedPlanDate && formattedContractDate
      ? getDateDifference(formattedPlanDate, formattedContractDate)
      : formattedPlanDate || formattedContractDate
        ? 0
        : null
  }

  if (['plan_end_date', 'contract_end_date'].some((key) => key in values)) {
    if (status?.MasterProjectStageStatus?.project_stage_status === CONSTRUCTION) {
      basicDetails.buffer_in_days = calculateVariance('plan_end_date', 'contract_end_date')
    }
  }

  // NOTE : variance_in_days is auto calculated field from backend
  // if (
  //   [
  //     'plan_end_date',
  //     'forecasted_end_date',
  //     'baseline_plan_finish',
  //     'forecast_finish',
  //     'contract_start_date',
  //     'contract_end_date',
  //   ].some((key) => key in values)
  // ) {
  //   // if (status?.stage_status === CONSTRUCTION) {
  //   //   basicDetails.variance_in_days = calculateVariance('contract_start_date', 'contract_end_date')
  //   // } else {
  //   if (isContractDlp.includes(status?.stage_status)) {
  //     basicDetails.variance_in_days = calculateVariance('forecasted_end_date', 'plan_end_date')
  //   }
  //   if (isInitiationDesignLdc.includes(status?.stage_status)) {
  //     basicDetails.variance_in_days = calculateVariance('forecast_finish', 'baseline_plan_finish')
  //   }
  //   if (status?.stage_status === CONSTRUCTION) {
  //     basicDetails.variance_in_days = calculateVariance('forecasted_end_date', 'contract_end_date')
  //   }
  // }
  basicDetailsFields.forEach((field) => {
    if (field in values) {
      basicDetails[field] = values[field] || null
    }
  })
  // if (values?.time_elapsed_perc) basicDetails.time_elapsed_perc = values.time_elapsed_perc.toString()
  if (highlightValue && isUpdateHighlight) {
    basicDetails.key_highlights = highlightValue ? highlightValue : null
  }

  // check forecast_finish is change or not
  // check updated forecast_finish is greater than forecast_completion_last_week date or not
  if (
    values.slippage_justification &&
    slippageJustificationValidation(
      status?.stage_status,
      formik.values.forecast_finish,
      status?.forecast_completion_last_week,
    )
  ) {
    basicDetails.slippage_justification = values.slippage_justification
  }

  // USE_FOR: This field is calculated based on the stage status using the getAdditionData function.
  const additionalData = getAdditionalData(values, status)
  const payload = { ...additionalData, ...basicDetails }
  delete payload.design_project_manager_name

  // USE_FOR: Compare excluding key_highlights to determine the last update time and date in basic details.
  const payloadWithoutHighlights: any = { ...payload }
  delete payloadWithoutHighlights.key_highlights

  const initialValuesWithoutHighlights = { ...formik.initialValues }
  delete initialValuesWithoutHighlights.key_highlights

  const hasHighlightChanged = formik.initialValues['key_highlights'] !== highlightValue
  const hasOtherValuesChanged = Object.keys(payloadWithoutHighlights).length > 0

  let hasChanges = false

  const logDifferences = () => {
    const data = { ...payload }

    if (Object.keys(data).length > 0) {
      hasChanges = true
    }
  }

  logDifferences()

  if (hasChanges) {
    // Determine key_highlights_last_updated and last_updated
    if (hasHighlightChanged) {
      payload['key_highlights_last_updated'] = new Date()

      payload['section'] = hasOtherValuesChanged ? null : 'key_highlights'
    } else if (hasOtherValuesChanged) {
      payload['key_highlights_last_updated'] = values.key_highlights_last_updated
      payload['last_updated'] = new Date()
      payload['section'] = 'normal'
    } else {
      payload['key_highlights_last_updated'] = values.key_highlights_last_updated
    }
    payload['period'] = currentPeriod
    payload['project_name'] = status?.project_name
    payload['master_project_stage_status_id'] = status?.MasterProjectStageStatus?.id

    if (hasOtherValuesChanged) payload['last_updated'] = new Date()

    const response: Record<string, any> = status
      ? await updateStatusApi({ id: status.id as unknown as number, data: { ...payload } })
      : await addStatusApi({ data: { ...payload } })

    if (response.payload.success) {
      const statusRes: Record<string, any> = await getStatusApi({
        period: currentPeriod,
        project_name: status?.project_name,
      })
      // if (statusRes.payload.success) {
      //   await updateProjectStatusAutomation(
      //     currentProject,
      //     statusRes.payload.data,
      //     updateMasterProjectApi,
      //     currentPeriod,
      //   )
      return statusRes.payload.success
      // }
    } else {
      showCustomToast('Error', response?.payload?.response?.data?.message || 'error')
    }
  }
  setIsUpdateHighlight(false)
}

// Conditional data based on stage
const getContractorData = (values: any): any => {
  const data: any = {}
  if ('actual_plan_percentage' in values) {
    data.actual_plan_percentage = values.actual_plan_percentage
      ? parseFloat((Number(values.actual_plan_percentage) / 100).toFixed(4))
      : null
  }
  if ('revised_plan_percentage' in values) {
    data.revised_plan_percentage = values.revised_plan_percentage
      ? parseFloat((Number(values.revised_plan_percentage) / 100).toFixed(4))
      : null
  }
  if ('plan_end_date' in values) {
    data.plan_end_date = values.plan_end_date ? payloadDateFormate(values.plan_end_date) : null
  }
  if ('revised_plan_end_date' in values) {
    data.revised_plan_end_date = values.revised_plan_end_date ? payloadDateFormate(values.revised_plan_end_date) : null
  }
  if ('forecasted_end_date' in values) {
    data.forecasted_end_date = values.forecasted_end_date ? payloadDateFormate(values.forecasted_end_date) : null
  }
  return data
}

const getLDCData = (values: any): any => {
  const data: any = {}
  if ('actual_percentage' in values) {
    data.actual_percentage = values.actual_percentage
      ? parseFloat((Number(values.actual_percentage) / 100).toFixed(4))
      : null
  }
  if ('rev_plan_percentage' in values) {
    data.rev_plan_percentage = values.rev_plan_percentage
      ? parseFloat((Number(values.rev_plan_percentage) / 100).toFixed(4))
      : 0
  }
  if ('baseline_plan_finish' in values) {
    data.baseline_plan_finish = values.baseline_plan_finish ? payloadDateFormate(values.baseline_plan_finish) : null
  }
  if ('revised_baseline_finish' in values) {
    data.revised_baseline_finish = values.revised_baseline_finish
      ? payloadDateFormate(values.revised_baseline_finish)
      : null
  }
  if ('forecast_finish' in values) {
    data.forecast_finish = values.forecast_finish ? payloadDateFormate(values.forecast_finish) : null
  }
  return data
}

export const getAdditionalData = (values: any, status: IStatus): any => {
  const stage = status?.MasterProjectStageStatus?.project_stage_status

  switch (stage) {
    case CONTRACTOR_PROCUREMENT:
    case CONSTRUCTION:
    case DLP_PROJECT_CLOSEOUT:
      return getContractorData(values)
    case LDC_PROCUREMENT:
    case DESIGN:
    case INITIATION:
      return getLDCData(values)
    default:
      return {}
  }
}

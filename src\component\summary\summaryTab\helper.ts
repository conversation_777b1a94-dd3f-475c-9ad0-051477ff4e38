import { FormikProps, FormikValues } from 'formik'
import {
  excludeProjectStatusFields,
  governanceField,
  healthAndSafetyFields,
  projectDetailsField,
  projectManagementField,
  sustainabilityField,
  unnecessaryOfSummaryFormField,
} from './constant'
import { IValidationState } from './interface'
import { showCustomToast } from '../../toast/ToastManager'
import { determineProjectStatus, generateAutoValueOfProject } from '../helper'
import { ERROR_MESSAGE, STATUS_OPTIONS } from '@/src/constant/enum'
import { sanitizeObject } from '@/src/helpers/helpers'
import { IGovernance } from '@/src/redux/governance/interface'
import { IStatus } from '@/src/redux/status/interface'
import { ISustainability } from '@/src/redux/sustainability/interface'
import { IProjects } from '@/src/services/projects/interface'
import { arrayToStringForTable } from '@/src/utils/arrayUtils'
import {
  checkLastDateGreaterOrEqual,
  DDMMYYYYformate,
  getMaxDateWithIndex,
  isDateBefore,
  isoToYYYYMMDD,
  jsDateToDDMMYYYY,
  payloadDateFormate,
  toISOFormatDate,
  isoToPayload,
} from '@/src/utils/dateUtils'
import { errorToast, successToast } from '@/src/utils/toastUtils'

export const calculateTotalGovernanceSum = (values: any): number => {
  const initiation = Number(values?.initiation) || 0
  const ldc = Number(values?.ldc_procurement) || 0
  const design = Number(values?.design) || 0
  const contractor = Number(values?.contractor_procurement) || 0
  const construction = Number(values?.construction) || 0
  const handover = Number(values?.handover) || 0

  const sum = initiation + ldc + design + contractor + construction + handover

  return sum || 0 // Return 0 if the sum is 0 or undefined
}

export const generateProjectPayload = (
  updatedValues: { [key: string]: any },
  currentPeriod: string,
  formValues: { [key: string]: any },
  automationProjectStatus: string,
) => {
  const recordWithUpdatedValues: any = {
    ...updatedValues,
    mitigation_recovery_plan_ids: updatedValues?.mitigation_recovery_plan_ids,
    next_steps_to_advance_progress_ids: updatedValues?.next_steps_to_advance_progress_ids,
    non_recoverable_delay_justification_ids: updatedValues?.non_recoverable_delay_justification_ids,
    reason_for_delay_ids: updatedValues?.reason_for_delay_ids,
    is_executive_project: Boolean(updatedValues?.is_executive_project),
    primary_reason_for_delay_ids: Array.isArray(updatedValues?.primary_reason_for_delay_ids)
      ? updatedValues?.primary_reason_for_delay_ids
      : [updatedValues?.primary_reason_for_delay_ids],
    latitude: parseFloat(updatedValues?.latitude) || 0,
    longitude: parseFloat(updatedValues?.longitude) || 0,
    project_status:
      formValues.project_status === STATUS_OPTIONS.RESUME
        ? determineProjectStatus(formValues.project_status as string, automationProjectStatus)
        : formValues.project_status,
    is_subject_to_rebaseline: formValues.is_subject_to_rebaseline,
    // is_subject_to_rebaseline: formValues.is_subject_to_rebaseline ? 'Subject to Rebaseline' : '',
    period: currentPeriod,
  }

  if ('recycled_material_percentage' in updatedValues) {
    recordWithUpdatedValues.recycled_material_percentage = updatedValues.recycled_material_percentage
      ? parseFloat(updatedValues.recycled_material_percentage)
      : null
  }

  if ('pearl_rating_percentage' in updatedValues) {
    recordWithUpdatedValues.pearl_rating_percentage = updatedValues.pearl_rating_percentage
      ? parseFloat(updatedValues.pearl_rating_percentage)
      : null
  }

  if ('potential_budget_uplift_value' in updatedValues) {
    recordWithUpdatedValues.potential_budget_uplift_value = updatedValues.potential_budget_uplift_value
      ? parseFloat(updatedValues.potential_budget_uplift_value)
      : null
  }
  if ('uplifted_budget' in updatedValues) {
    recordWithUpdatedValues.uplifted_budget = updatedValues.uplifted_budget
      ? parseFloat(updatedValues.uplifted_budget)
      : null
  }
  if ('decree_date' in updatedValues) {
    recordWithUpdatedValues.decree_date = updatedValues.decree_date ? isoToYYYYMMDD(updatedValues.decree_date) : null
  }
  if ('commencement_date' in updatedValues) {
    recordWithUpdatedValues.commencement_date = updatedValues.commencement_date
      ? isoToYYYYMMDD(updatedValues.commencement_date)
      : null
  }
  if ('budget_uplift_submission_date' in updatedValues) {
    recordWithUpdatedValues.budget_uplift_submission_date = updatedValues.budget_uplift_submission_date
      ? isoToYYYYMMDD(updatedValues.budget_uplift_submission_date)
      : null
  }
  if ('budget_uplift_last_submission_date' in updatedValues) {
    recordWithUpdatedValues.budget_uplift_last_submission_date = updatedValues.budget_uplift_last_submission_date
      ? isoToYYYYMMDD(updatedValues.budget_uplift_last_submission_date)
      : null
  }
  if ('bond_expiry_date' in updatedValues) {
    recordWithUpdatedValues.bond_expiry_date = updatedValues.bond_expiry_date
      ? isoToYYYYMMDD(updatedValues.bond_expiry_date)
      : null
  }
  if ('potential_budget_uplift_value' in updatedValues) {
    recordWithUpdatedValues.potential_budget_uplift_value = updatedValues.potential_budget_uplift_value
      ? parseFloat(updatedValues.potential_budget_uplift_value)
      : null
  }
  if ('eot_submission_date' in updatedValues) {
    recordWithUpdatedValues.eot_submission_date = updatedValues.eot_submission_date
      ? isoToYYYYMMDD(updatedValues.eot_submission_date)
      : null
  }
  if ('submitted_eot_date' in updatedValues) {
    recordWithUpdatedValues.submitted_eot_date = updatedValues.submitted_eot_date
      ? isoToYYYYMMDD(updatedValues.submitted_eot_date)
      : null
  }
  if ('eot_last_submission_date' in updatedValues) {
    recordWithUpdatedValues.eot_last_submission_date = updatedValues.eot_last_submission_date
      ? isoToPayload(updatedValues.eot_last_submission_date)
      : null
  }
  if ('tip_submission_date' in updatedValues) {
    recordWithUpdatedValues.tip_submission_date = updatedValues.tip_submission_date
      ? isoToYYYYMMDD(updatedValues.tip_submission_date)
      : null
  }
  if ('tip_approval_date' in updatedValues) {
    recordWithUpdatedValues.tip_approval_date = updatedValues.tip_approval_date
      ? isoToYYYYMMDD(updatedValues.tip_approval_date)
      : null
  }

  if ('tip_last_submission_date' in updatedValues) {
    recordWithUpdatedValues.tip_last_submission_date = updatedValues.tip_last_submission_date
      ? isoToPayload(updatedValues.tip_last_submission_date)
      : null
  }
  if ('eot_approval_date' in updatedValues) {
    recordWithUpdatedValues.eot_approval_date = updatedValues.eot_approval_date
      ? isoToYYYYMMDD(updatedValues.eot_approval_date)
      : null
  }
  if ('scope_change_submission_date' in updatedValues) {
    recordWithUpdatedValues.scope_change_submission_date = updatedValues.scope_change_submission_date
      ? isoToYYYYMMDD(updatedValues.scope_change_submission_date)
      : null
  }
  if ('scope_change_approval_date' in updatedValues) {
    recordWithUpdatedValues.scope_change_approval_date = updatedValues.scope_change_approval_date
      ? isoToYYYYMMDD(updatedValues.scope_change_approval_date)
      : null
  }
  if ('scope_change_last_submission_date' in updatedValues) {
    recordWithUpdatedValues.scope_change_last_submission_date = updatedValues.scope_change_last_submission_date
      ? isoToPayload(updatedValues.scope_change_last_submission_date)
      : null
  }
  if ('budget_uplift_approval_date' in updatedValues) {
    recordWithUpdatedValues.budget_uplift_approval_date = updatedValues.budget_uplift_approval_date
      ? isoToYYYYMMDD(updatedValues.budget_uplift_approval_date)
      : null
  }
  if ('overall_planned_finish_date' in updatedValues) {
    recordWithUpdatedValues.overall_planned_finish_date = updatedValues.overall_planned_finish_date
      ? isoToYYYYMMDD(updatedValues.overall_planned_finish_date)
      : null
  }
  if ('overall_forecasted_finish_date' in updatedValues) {
    recordWithUpdatedValues.overall_forecasted_finish_date = updatedValues.overall_forecasted_finish_date
      ? isoToYYYYMMDD(updatedValues.overall_forecasted_finish_date)
      : null
  }
  if ('decree_end_date' in updatedValues) {
    recordWithUpdatedValues.decree_end_date = updatedValues.decree_end_date
      ? isoToYYYYMMDD(updatedValues.decree_end_date)
      : null
  }

  healthAndSafetyFields.forEach((field) => {
    if (field === 'is_external_lookup_for_health_and_safety') {
      recordWithUpdatedValues[field] = formValues[field]
    } else if (field in formValues) {
      recordWithUpdatedValues[field] = formValues[field] || null
    }
  })

  return Object.keys(recordWithUpdatedValues)
    .filter((key) => updatedValues.hasOwnProperty(key) && !unnecessaryOfSummaryFormField.includes(key))
    .reduce((acc: any, key: any) => {
      acc[key] = recordWithUpdatedValues[key]
      return acc
    }, {})
}

const formatPercentage = (value: number | string) => parseFloat((Number(value) / 100).toFixed(4))
const formatCurrency = (value: string) => Number(value.toString().replace(/,/g, ''))
const formateNumber = (val: any) => Number(val)?.toString()

export const generateSustainabilityPayload = (
  values: { [key: string]: any },
  currentPeriod: string,
  projectName: string,
) => {
  const result: any = {
    period: currentPeriod,
    project_name: projectName,
    last_updated: new Date(),
  }
  const fields = [
    { key: 'waste_recycled_percentage', transform: formatPercentage },
    { key: 'reinvented_economy_percentage', transform: formatPercentage },
    { key: 'reinvented_economy_value', transform: formatCurrency },
    { key: 'pearl_rating_percentage', transform: (val: any) => Number(val) },
    { key: 'recycled_material_percentage', transform: formatPercentage },
    { key: 'workers_welfare_compliance_percentage', transform: formatPercentage },
    { key: 'renewable_energy', transform: formateNumber },
    { key: 'carbon_emissions', transform: formateNumber },
    { key: 'emissions_per_m2', transform: formateNumber },
    { key: 'number_of_grievances', transform: formateNumber },
  ]

  // Process fields
  fields.forEach(({ key, transform }) => {
    if (key in values) {
      result[key] = values[key].toString() ? transform(values[key]) : null
    }
  })

  return result
}

export const generateGovernancePayload = (
  values: { [key: string]: any },
  currentPeriod: string,
  projectName: string,
) => {
  const result: any = {
    period: currentPeriod,
    project_name: projectName,
    last_updated: new Date(),
  }

  const percentageFields = [
    'initiation',
    'ldc_procurement',
    'design',
    'contractor_procurement',
    'construction',
    'handover',
  ]
  // Process percentage fields
  percentageFields.forEach((field) => {
    if (field in values) {
      result[field] = values[field].toString() ? formatPercentage(values[field]) : null
    }
  })
  return result
}

export const resetGovernanceFields = (formik: any) => {
  const updatedValues = { ...formik.values } // Start with current values

  governanceField.forEach((field: string) => {
    updatedValues[field] = formik.initialValues[field] // Reset only specified fields
  })

  formik.setValues(updatedValues) // Apply all updates at once
}

const checkIsWeekEnd = (date: Date) => {
  return date.getDay() === 0 || date.getDay() === 6
}

//* Validation For Approval Date, Submission Date, and Last Submission Date
export const checkDatesValidation = (data: {
  approvalDate: string
  submissionDate: string | undefined
  lastSubmissionDate: string | undefined
  name: string
}) => {
  const { approvalDate, submissionDate, lastSubmissionDate, name } = data

  //* If both submissionDate and lastSubmissionDate available, then last submission date must be greater than or equal to submission date
  if (submissionDate && lastSubmissionDate) {
    const isLastDateGreaterOrEqual = checkLastDateGreaterOrEqual(submissionDate, lastSubmissionDate)
    if (!isLastDateGreaterOrEqual) {
      return `${name} Last Submission Date must be greater than or equal its First Submission Date.`
    }
  }

  if (approvalDate) {
    const getMaxTIPDate = getMaxDateWithIndex(submissionDate || '', lastSubmissionDate || '')

    if (getMaxTIPDate) {
      const isLastDateGreaterOrEqual = checkLastDateGreaterOrEqual(getMaxTIPDate.maxDate, approvalDate)
      if (!isLastDateGreaterOrEqual) {
        //* must be greater than or equal to its Submission Date.
        const message =
          getMaxTIPDate.index === 0
            ? `${name} Approval Date must be greater than or equal to its First Submission Date.`
            : `${name} Approval Date must be greater than or equal to its Last Submission Date.`
        console.groupEnd()

        return message
      }
    }
  }
  console.groupEnd()

  return null // Return null if no validation error
}

export const summarySubmit = async (
  updatedFields: { [key: string]: any },
  formValues: { [key: string]: any },
  formik: FormikProps<FormikValues>,
  currentPeriod: any,
  statuses: any,
  project: any,
  updateProject: any,
  sustainabilities: any,
  addSustainabilityApi: any,
  updateSustainabilityApi: any,
  getSustainabilitiesApi: any,
  governances: any,
  getGovernanceApi: any,
  addGovernanceApi: any,
  updateGovernanceApi: any,
  setLoader: any,
  setSummaryEdit: (args: boolean) => void,
  router: any,
  validation: IValidationState,
  setValidation: React.Dispatch<React.SetStateAction<IValidationState>>,
) => {
  const projectName = formValues.project_name
  const { automationProjectStatus } = generateAutoValueOfProject(statuses, project)
  const projectPayload = generateProjectPayload(updatedFields, currentPeriod, formValues, automationProjectStatus)

  //  ################# Validation Logic ##################### start
  // const lastSubmissionDate = payloadDateFormate(formValues.budget_uplift_last_submission_date) as string
  // const firstSubmissionDate = payloadDateFormate(formValues.budget_uplift_submission_date) as string
  const governance = governances.find((item: IGovernance) => item.project_name === router.query.slug)
  const governanceChanged = governanceField.some(
    (field) => formik.initialValues[field]?.toString() !== formValues[field]?.toString(),
  )
  let totalSumOfGovernance = calculateTotalGovernanceSum(formik?.values)

  const initiationsStatus = statuses.find((item: IStatus) => {
    if (item.stage_status?.toLowerCase() === 'initiation') {
      return true
    }
    return false
  })

  const initiationsEndDates = {
    plan: initiationsStatus?.baseline_plan_finish,
    forecast: initiationsStatus?.forecast_finish,
  }

  // if decreedate and commencementdate is greater than the intiation end dates, then show validation error

  if (formik?.values.decree_date) {
    const decreeDate = toISOFormatDate(
      ('' + formik?.values.decree_date)?.includes('GMT')
        ? jsDateToDDMMYYYY(formik?.values.decree_date)
        : formik?.values.decree_date,
    )

    if (initiationsEndDates.plan?.length > 0 && !isDateBefore(decreeDate, initiationsEndDates.plan)) {
      setValidation((prevState) => ({
        ...prevState,
        open: true,
        message: ['Decree Date cannot be greater than the Initiation Baseline Plan Finish Date'],
      }))
      setLoader(false)
      return
    }
  }

  if (formik?.values.commencement_date) {
    const commencementDate = toISOFormatDate(
      ('' + formik?.values.commencement_date)?.includes('GMT')
        ? jsDateToDDMMYYYY(formik?.values.commencement_date)
        : formik?.values.commencement_date,
    )

    if (initiationsEndDates.forecast?.length > 0 && !isDateBefore(commencementDate, initiationsEndDates.forecast)) {
      setValidation((prevState) => ({
        ...prevState,
        open: true,
        message: ['Commencement Date cannot be greater than the Initiation Forecast Finish Date'],
      }))
      setLoader(false)
      return
    }
  }

  // if (checkIsWeekEnd(new Date(firstSubmissionDate))) {
  //   setValidation((prevState) => ({
  //     ...prevState,
  //     open: true,
  //     message: ['"First Submission Date" cannot be a weekend.'],
  //   }))
  //   setLoader(false)
  //   return
  // }

  // if (checkIsWeekEnd(new Date(lastSubmissionDate))) {
  //   setValidation((prevState) => ({
  //     ...prevState,
  //     open: true,
  //     message: ['"Last Submission Date" cannot be a weekend.'],
  //   }))
  //   setLoader(false)
  //   return
  // }
  const isBudgetUpliftSubmitted = projectPayload.budget_uplift_submitted ?? formValues.budget_uplift_submitted
  const budgetUpliftSubmissionDate =
    projectPayload.budget_uplift_submission_date ?? formValues.budget_uplift_submission_date
  const budgetUpliftLastSubmissionDate =
    projectPayload.budget_uplift_last_submission_date ?? formValues.budget_uplift_last_submission_date
  const budgetUpliftApprovalDate = projectPayload.budget_uplift_approval_date ?? formValues.budget_uplift_approval_date
  if (isBudgetUpliftSubmitted && !budgetUpliftSubmissionDate) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: ['Budget uplift Submission Date is required when selecting Budget Uplift Submission.'],
    }))
    setLoader(false)
    return
  }

  const tip_submitted = projectPayload.tip_submitted ?? formValues.tip_submitted
  const tip_submission_date = projectPayload.tip_submission_date ?? formValues.tip_submission_date
  const tip_last_submission_date = projectPayload.tip_last_submission_date ?? formValues.tip_last_submission_date
  const tip_approval_date = projectPayload.tip_approval_date ?? formValues.tip_approval_date
  if (tip_submitted && !tip_submission_date) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: ['Transfer In Progress Submission Date is required when selecting Transfer In Progress Submission.'],
    }))
    setLoader(false)
    return
  }

  const eot_submitted = projectPayload.eot_submitted ?? formValues.eot_submitted
  const eot_submission_date = projectPayload.eot_submission_date ?? formValues.eot_submission_date
  const eot_last_submission_date = projectPayload.eot_last_submission_date ?? formValues.eot_last_submission_date
  const eot_approval_date = projectPayload.eot_approval_date ?? formValues.eot_approval_date
  if (eot_submitted && !eot_submission_date) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: ['EOT Submission Date is required when selecting EOT Submission.'],
    }))
    setLoader(false)
    return
  }

  const scope_change = projectPayload.scope_change ?? formValues.scope_change
  const scope_change_submission_date =
    projectPayload.scope_change_submission_date ?? formValues.scope_change_submission_date
  const scope_change_approval_date = projectPayload.scope_change_approval_date ?? formValues.scope_change_approval_date
  const scope_change_last_submission_date =
    projectPayload.scope_change_last_submission_date ?? formValues.scope_change_last_submission_date
  if (scope_change && !scope_change_submission_date) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: ['Scope Change Submission Date is required when selecting Scope Change.'],
    }))
    setLoader(false)
    return
  }

  if (totalSumOfGovernance > 100 && governanceChanged) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: ['The total percentage of governance values should not exceed 100%.'],
    }))
    resetGovernanceFields(formik)
    setLoader(false)
    return
  }

  //* TIP Date Validation
  const tipDatesValidation = checkDatesValidation({
    approvalDate: tip_approval_date,
    submissionDate: tip_submission_date,
    lastSubmissionDate: tip_last_submission_date,
    name: 'Transfer In Progress',
  })

  //* EOT Date Validation
  const eotDatesValidation = checkDatesValidation({
    approvalDate: eot_approval_date,
    submissionDate: eot_submission_date,
    lastSubmissionDate: eot_last_submission_date,
    name: 'eot',
  })

  //* Scope Change Date Validation
  const scopeDatesValidation = checkDatesValidation({
    approvalDate: scope_change_approval_date,
    submissionDate: scope_change_submission_date,
    lastSubmissionDate: scope_change_last_submission_date,
    name: 'Scope Change',
  })

  //* Budget Uplift Date Validation
  const budgetDatesValidation = checkDatesValidation({
    approvalDate: budgetUpliftApprovalDate,
    submissionDate: budgetUpliftSubmissionDate,
    lastSubmissionDate: budgetUpliftLastSubmissionDate,
    name: 'Budget Uplift',
  })

  const datesValidator: string[] = [
    tipDatesValidation,
    eotDatesValidation,
    scopeDatesValidation,
    budgetDatesValidation,
  ].filter((msg): msg is string => msg !== null)

  if (datesValidator.length > 0) {
    setValidation((prevState) => ({
      ...prevState,
      open: true,
      message: datesValidator,
    }))
    setLoader(false)
    return
  }

  setSummaryEdit(false)

  //  ################# Validation Logic ##################### End

  // If the approval date exists for budget uplift, TIP, or EOT,
  // mark their respective "submitted" flags as false to ensure
  // they are not considered submitted after approval.
  if (projectPayload?.budget_uplift_approval_date) {
    projectPayload.budget_uplift_submitted = false
  }
  if (projectPayload?.tip_approval_date) {
    projectPayload.tip_submitted = false
  }
  if (projectPayload?.eot_approval_date) {
    projectPayload.eot_submitted = false
  }
  if (projectPayload?.scope_change_approval_date) {
    projectPayload.scope_change = false
  }

  const sectionsToUpdate: string[] = []

  if (
    //check any values is change from the HEALTH AND SAFETY section
    Object.keys(projectPayload).some((key) => healthAndSafetyFields.includes(key))
  ) {
    projectPayload['health_safety_last_updated'] = new Date().toISOString()
    sectionsToUpdate.push('health_safety')
  }

  if (
    //check any values is change from the PROJECT MANAGEMENT section
    Object.keys(projectPayload).some((key) => projectManagementField.includes(key))
  ) {
    projectPayload['project_management_last_updated'] = new Date().toISOString()
    sectionsToUpdate.push('project_management')
  }

  if (
    //check any values is change from the project details section
    Object.keys(projectPayload).some((key) => projectDetailsField.includes(key))
  ) {
    projectPayload['last_updated'] = new Date()
    sectionsToUpdate.push('normal') // when basic details values updated that type we need to pass normal
  }

  if (sectionsToUpdate.length > 0) {
    projectPayload['section'] = sectionsToUpdate.join(',')
  }

  // 'projectPayload' contains data for three sections: Health and Safety, Project Management, and Project Details.
  // All the updated values from these sections are sent via a single API call to the 'updateProjectApi'.
  // The API is responsible for updating all three sections in the backend.
  if (Object.keys(projectPayload).length > 0) {
    const fieldsToRemove: any = [
      'decree_files',
      'plot_plan_files',
      'static_images_files',
      'dynamic_images_files',
      'progress_videos',
      'additional_documents_files',
      'project_management_files',
      'ProcurementManagers',
      'MediaFiles',
    ]
    fieldsToRemove.forEach((field: any) => delete projectPayload[field])
    updateProject(
      {
        id: project.project_name as string,
        data: { ...projectPayload, period: currentPeriod },
      },
      {
        onSuccess: () => {
          successToast('Project updated successfully!')
        },
        onError: (err: any) => {
          formik.resetForm()
          errorToast(err.response?.data?.message ? err.response?.data?.message : ERROR_MESSAGE)
        },
      },
    )
  }

  // Handle Sustainability
  // First, we check if sustainability data already exists for the project by looking it up using 'project_name'.
  // If sustainability data is available, we check if any relevant fields have changed.
  // If changes are detected, we either update the existing sustainability record or create a new one
  // (if no record exists) using the appropriate API call. The API is only triggered when data changes.
  const sustainability = sustainabilities.find((item: ISustainability) => item.project_name === router.query.slug)
  const sustainabilityChanged = sustainabilityField.some(
    (field) => formik.initialValues[field]?.toString() !== formValues[field]?.toString(),
  )

  if (sustainabilityChanged) {
    try {
      setLoader(true)
      if (sustainability) {
        const responseOfSustainability: Record<string, any> = await updateSustainabilityApi({
          id: formValues.sustainabilityId,
          data: generateSustainabilityPayload(updatedFields, currentPeriod, projectName),
        })
        if (responseOfSustainability.payload.success === true) {
          currentPeriod && getSustainabilitiesApi({ period: currentPeriod })
          setLoader(false)
        } else {
          showCustomToast('Error', 'error')
          // handleSubmissionFailure()
        }
      } else {
        const responseOfSustainability: Record<string, any> = await addSustainabilityApi(
          generateSustainabilityPayload(updatedFields, currentPeriod, projectName),
        )
        if (responseOfSustainability.payload.success === true) {
          currentPeriod && getSustainabilitiesApi({ period: currentPeriod })
          setLoader(false)
        } else {
          showCustomToast('Error', 'error')
          // handleSubmissionFailure()
        }
      }
    } catch (error) {
      // handleSubmissionFailure()
    } finally {
      setLoader(false)
    }
  }

  // Handle Governance
  //it is same like sustainability is working

  if (governanceChanged) {
    try {
      setLoader(true)
      if (governance) {
        const responseOfSustainability: Record<string, any> = await updateGovernanceApi({
          id: governance.id,
          ...generateGovernancePayload(updatedFields, currentPeriod, projectName),
        })
        if (responseOfSustainability.payload.success === true) {
          currentPeriod && getGovernanceApi({ period: currentPeriod })
          setLoader(false)
        } else {
          showCustomToast('Error', 'error')
          // handleSubmissionFailure()
        }
      } else {
        const responseOfSustainability: Record<string, any> = await addGovernanceApi(
          generateGovernancePayload(updatedFields, currentPeriod, projectName),
        )
        if (responseOfSustainability.payload.success === true) {
          currentPeriod && getGovernanceApi({ period: currentPeriod })
          setLoader(false)
        } else {
          showCustomToast('Error', 'error')
          // handleSubmissionFailure()
        }
      }
    } catch (error) {
      // handleSubmissionFailure()
    } finally {
      setLoader(false)
    }
  }
  setLoader(false)
}

export const getInitialValues = (
  project: IProjects,
  sustainability: ISustainability,
  governance: IGovernance,
  statuses: IStatus[],
) => {
  const { automationForecastedDate, automationPlanDate, automationProjectStatus } = generateAutoValueOfProject(
    statuses,
    project,
  )

  const payload = {
    ...project,
    ...sustainability,
    ...governance,
    next_steps_to_advance_progress_ids: project?.NextStepsToAdvanceProgress?.map((item: any) => item.id) || [],
    mitigation_recovery_plan_ids: project?.MitigationRecoveryPlans?.map((item: any) => item.id) || [],
    non_recoverable_delay_justification_ids:
      project?.NonRecoverableDelayJustifications?.map((item: any) => item.id) || [],
    reason_for_delay_ids: project?.ReasonsForDelay?.map((item: any) => item.id) || [],
    primary_reason_for_delay_ids: project?.PrimaryReasonsForDelay?.map((item: any) => item.id) || [],
    incidents: project?.incidents?.toString() ? project?.incidents : 0,
    lti: project?.lti?.toString() ? project?.lti : 0,
    man_hours: project?.man_hours?.toString() ? project?.man_hours : 0,
    actual_manpower: project?.actual_manpower?.toString() ? project?.actual_manpower : 0,
    planned_manpower: project?.planned_manpower?.toString() ? project?.planned_manpower : 0,
    // project_status: determineProjectStatus(project?.project_status as string, automationProjectStatus),
    project_status: project?.project_status,
    isExecutiveProject: Number(project?.isExecutiveProject) ? 1 : 0,
    executive_sorting_order: project?.executive_sorting_order ? project?.executive_sorting_order : 0,
    location:
      project.Locations && project?.Locations?.length > 0 ? arrayToStringForTable(project?.Locations, 'location') : '',
    sub_location_ids:
      project?.SubLocations && project?.SubLocations?.length > 0
        ? project?.SubLocations?.map((item: any) => item?.id)
        : '',
    // Overall_Forecasted_Finish_: automationForecastedDate as string,
    // Overall_Planned_Finish_Date: automationPlanDate as string,
    Overall_Forecasted_Finish_: DDMMYYYYformate(project?.overall_forecasted_finish_date as string),
    overall_planned_finish_date: DDMMYYYYformate(project?.overall_planned_finish_date as string),
    decree_date: project?.decree_date ? DDMMYYYYformate(project?.decree_date?.toString()) : null,
    commencement_date: DDMMYYYYformate(project?.commencement_date as string),
    budget_uplift_submission_date: DDMMYYYYformate(project?.budget_uplift_submission_date as string),
    budget_uplift_last_submission_date: DDMMYYYYformate(project?.budget_uplift_last_submission_date as string),
    bond_expiry_date: DDMMYYYYformate(project?.bond_expiry_date as string),
    budget_uplift_approval_date: DDMMYYYYformate(project?.budget_uplift_approval_date as string),
    // TODO
    eot_approval_date: DDMMYYYYformate(project?.eot_approval_date as string),
    eot_submission_date: DDMMYYYYformate(project?.eot_submission_date as string),
    submitted_eot_date: DDMMYYYYformate(project?.submitted_eot_date as string),
    eot_last_submission_date: DDMMYYYYformate(project?.eot_last_submission_date as string),
    tip_submission_date: DDMMYYYYformate(project?.tip_submission_date as string),
    tip_approval_date: DDMMYYYYformate(project?.tip_approval_date as string),
    tip_last_submission_date: DDMMYYYYformate(project?.tip_last_submission_date as string),
    scope_change_submission_date: DDMMYYYYformate(project?.scope_change_submission_date as string),
    scope_change_approval_date: DDMMYYYYformate(project?.scope_change_approval_date as string),
    scope_change_last_submission_date: DDMMYYYYformate(project?.scope_change_last_submission_date as string),
    is_external_lookup_for_health_and_safety: project?.is_external_lookup_for_health_and_safety ?? false,
    decree_end_date: project?.decree_end_date
      ? DDMMYYYYformate(project?.decree_end_date as string)
      : DDMMYYYYformate(automationPlanDate as string),
    waste_recycled_percentage: sustainability?.waste_recycled_percentage?.toString()
      ? (Number(sustainability?.waste_recycled_percentage) * 100).toFixed(2).toString()
      : '',
    reinvented_economy_percentage: sustainability?.reinvented_economy_percentage
      ? (Number(sustainability?.reinvented_economy_percentage) * 100).toFixed(2).toString()
      : '',
    reinvented_economy_value: sustainability?.reinvented_economy_value
      ? (sustainability?.reinvented_economy_value)
          .toString()
          .split('.')[0]
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : 0,
    workers_welfare_compliance_percentage: sustainability?.workers_welfare_compliance_percentage
      ? (Number(sustainability.workers_welfare_compliance_percentage) * 100).toFixed(2)
      : '0.00',
    pearl_rating_percentage: Number(sustainability?.pearl_rating_percentage)
      ? Number(sustainability?.pearl_rating_percentage)?.toString()
      : '0.00',

    recycled_material_percentage: sustainability?.recycled_material_percentage?.toString()
      ? (Number(sustainability?.recycled_material_percentage) * 100).toFixed(2).toString()
      : '0.00',
    renewable_energy: Number(sustainability?.renewable_energy)
      ? Number(sustainability?.renewable_energy)?.toString()
      : '0.00',
    carbon_emissions: Number(sustainability?.carbon_emissions)
      ? Number(sustainability?.carbon_emissions)?.toString()
      : '0.00',
    emissions_per_m2: Number(sustainability?.emissions_per_m2)
      ? Number(sustainability?.emissions_per_m2)?.toString()
      : '0.00',
    number_of_grievances: Number(sustainability?.number_of_grievances)
      ? Number(sustainability?.number_of_grievances)?.toString()
      : '0.00',
    governanceId: governance?.id,
    sustainabilityId: sustainability?.id,
    initiation: governance?.initiation ? (Number(governance?.initiation) * 100).toFixed(2).toString() : '',

    ldc_procurement: governance?.ldc_procurement
      ? (Number(governance?.ldc_procurement) * 100).toFixed(2).toString()
      : '',
    design: governance?.design ? (Number(governance?.design) * 100).toFixed(2).toString() : '',
    contractor_procurement: governance?.contractor_procurement
      ? (Number(governance?.contractor_procurement) * 100).toFixed(2).toString()
      : '',
    construction: governance?.construction ? (Number(governance?.construction) * 100).toFixed(2).toString() : '',
    handover: governance?.handover ? (Number(governance?.handover) * 100).toFixed(2).toString() : '',
    last_updated: governance?.last_updated,
    // potential_budget_uplift_reason
    // eot_notes
    // tip_notes
    // scope_change_notes
    // scope_change
  }
  // return sanitizeObject(payload, excludeProjectStatusFields)
  return payload
}

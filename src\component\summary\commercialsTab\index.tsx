import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import CommercialFields from './commercialFields'
import CommercialHeader from './commercialHeader'
import styles from './CommercialsTab.module.scss'
import ContractAndVos from './contractAndVos'
import stylesAccordion from './contractAndVos/ContractAndVos.module.scss'
import ContractVosHeader from './contractVosHeader'
import { ICommercial } from './interface'
import ConfirmDeleteModal from '../../confirmDeleteModal'
import ReplaceMultiPhaseModal from '../../ReplaceMultiPhaseModal'
import Button from '../../shared/button'
import Loader from '../../shared/loader'
import PulseAccordion from '../../shared/pulseAccordion'
import DropDownIcon from '../../svgImages/dropDownIcon'
import { showCustomToast } from '../../toast/ToastManager'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useCommercial from '@/src/redux/commercial/useCommercial'
import useContractorAndVos from '@/src/redux/contractorAndVos/useContractorAndVos'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import usePhase from '@/src/redux/phase/usePhase'
import useFile from '@/src/redux/uploadPicture/useFile'
import { getMasterOneProject } from '@/src/services/projects'
import { errorToast, successToast } from '@/src/utils/toastUtils'
import { canEditUser } from '@/src/utils/userUtils'

interface CommercialsTabProps {
  selectedTab: string
}

const CommercialsTab: React.FC<CommercialsTabProps> = ({ selectedTab }) => {
  const { commercials, getCommercialApi, addCommercialApi, updateCommercialApi, deleteCommercialApi } = useCommercial()
  const { contractorAndVoses, getContractorAndVosApi, getContractorAndVosStatus } = useContractorAndVos()
  const { updateOverallPhaseWithMultiPhaseApi } = useFile()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [deleteId, setDeleteId] = useState<number | null>(null)
  const [loader, setLoader] = useState(true)
  const router = useRouter()
  const dataRef = useRef<any>([])
  const formikRef = useRef<any>()
  const [commercialData, setCommercialData] = useState<any>()
  const [contracts, setContracts] = useState<any>([])
  const [isEditMode, setIsEditMode] = useState(false)
  const [expandedBasicDetails, setExpandedBasicDetails] = useState(true)
  const [expanded, setExpanded] = useState(true)
  const [addState, setAddSetTime] = useState(false)
  const [selectedPhase, setSelectedPhase] = useState<{
    label: string
    value: string
    id: number
  } | null>(null)
  const { currentUser } = useAuthorization()
  // STATE PURPOSE: Manages the expansion of contract & VOS sections.
  // - Controls a single contract VOS accordion.
  // - Handles the collapse and expansion of all accordions.
  const [contractVosAccordion, setContractVosAccordion] = useState<{ index: string | null; isAccordion: boolean }[]>([])
  const [isSubmittingPhase, setIsSubmittingPhase] = useState(false)

  const { getMasterPhaseApi, getMasterPhaseCategoryApi } = usePhase()

  useEffect(() => {
    // getMasterPhaseApi()
    getMasterPhaseCategoryApi({ period: currentPeriod as string, project_name: router.query.slug as string })
    // getMasterContractTypeApi()
    // getMasterConsultantsApi()
    // getMasterContractorsApi()
  }, [])

  useEffect(() => {
    if (dataRef) {
      dataRef.current = commercialData || []
    }
  }, [commercialData])

  const commercialDataProjectSummary = useMemo(() => {
    const commercial = commercials.find((project) => {
      return project.project_name === router.query.slug
    })
    return commercial
  }, [commercials])

  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  const initialValues = useMemo(() => {
    return {
      id: commercialData?.id || null,
      description: commercialData?.description ? commercialData?.description : null,
      budget: commercialData?.budget ? Number(commercialData?.budget).toFixed(2) : null,
      committed_cost: commercialData?.committed_cost ? Number(commercialData?.committed_cost).toFixed(2) : null,
      vowd: commercialData?.vowd ? Number(commercialData?.vowd).toFixed(2) : null,
      paid_amount: commercialData?.paid_amount ? Number(commercialData?.paid_amount).toFixed(2) : null,
      forecast_at_completion: commercialData?.forecast_at_completion,
      design_budget: commercialData?.design_budget ? Number(commercialData?.design_budget).toFixed(2) : null,
      construction_budget: commercialData?.construction_budget
        ? Number(commercialData?.construction_budget).toFixed(2)
        : null,
      supervision_budget: commercialData?.supervision_budget
        ? Number(commercialData?.supervision_budget).toFixed(2)
        : null,
      management_fees: commercialData?.management_fees ? Number(commercialData?.management_fees).toFixed(2) : null,
      contingency: commercialData?.contingency ? Number(commercialData?.contingency).toFixed(2) : null,
      year_planned_vowd: commercialData?.year_planned_vowd
        ? Number(commercialData?.year_planned_vowd).toFixed(2)
        : null,
      year_forecast_vowd: commercialData?.year_forecast_vowd,
      totalPVR: contracts.reduce((sum: any, item: any) => {
        const value = Number(item.pvr_value)
        return sum + (isNaN(value) ? 0 : value)
      }, 0),
    }
  }, [commercialData, contracts])

  const handleSubmit = async (values: any) => {
    delete values.totalPVR
    delete values.id
    if (commercialData?.id) {
      handleUpdateCommercial(commercialData?.id, {
        ...values,
        last_updated: new Date(),
        period: currentPeriod,
        project_name: commercialData?.project_name as string,
      })
    } else {
      handleAddCommercial({
        ...values,
        last_updated: new Date(),
        project_name: router.query.slug as string,
        period: currentPeriod,
      })
    }
    // setIsEditMode(false)
    // formik.resetForm()
  }

  const formik = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    // validationSchema,
    onSubmit: (values) => {
      handleSubmit(values)
    },
  })

  useEffect(() => {
    if (formikRef) {
      formikRef.current = formik || []
    }
  }, [formik])

  useEffect(() => {
    const filterCommercial: any = commercials.find((item) => {
      return item.project_name === router.query.slug
    })
    setCommercialData(filterCommercial)
  }, [commercials])

  useEffect(() => {
    const filterCommercial: any = contractorAndVoses.filter((item: any) => {
      return item.project_name === router.query.slug
    })
    setContracts(filterCommercial)
  }, [getContractorAndVosStatus])

  const fetchData = async () => {
    setLoader(true)
    try {
      if (selectedTab === 'Commercials' && currentPeriod) {
        await getCommercialApi({ period: currentPeriod, project_name: router?.query?.slug as string })
        await getContractorAndVosApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      }
    } catch (error) {
      console.error('An error occurred while fetching data of commercial tab screen:', error)
    } finally {
      setLoader(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [selectedTab, currentPeriod])

  const handleAddCommercial = async (values: ICommercial) => {
    const response: Record<string, any> = await addCommercialApi(values)
    if (response.payload.success === true) {
      getCommercialApi({ period: currentPeriod, project_name: router?.query?.slug as string })
    } else {
      errorToast(response?.payload?.response?.data?.message || 'Failed')
    }
    setIsEditMode(false)
  }

  const handleUpdateCommercial = async (id: number, values: ICommercial) => {
    const response: Record<string, any> = await updateCommercialApi(id, values)
    if (response.payload.success) {
      const res: Record<string, any> = await getCommercialApi({
        period: currentPeriod,
        project_name: router?.query?.slug as string,
      })
      if (res.payload.success) {
        const filterCommercial: any = res.payload.data.find((item: any) => {
          return item.project_name === router.query.slug
        })
        setCommercialData(filterCommercial)
        successToast(response?.payload?.message)
        setIsEditMode(false)
      }
    } else {
      errorToast(response?.payload?.response?.data?.message || 'Failed')
      setIsEditMode(false)
      formik.resetForm()
    }
  }

  const handleDeleteAction = async (Id: number) => {
    const response: any = await deleteCommercialApi(deleteId as number)
    if (response?.payload?.success === true) {
      getCommercialApi({ period: currentPeriod, project_name: router?.query?.slug as string })
      setDeleteModel(null)
    } else {
      showCustomToast('Error', 'error')
    }
  }

  const handleChangeMultiPhase = async () => {
    if (selectedPhase) {
      setIsSubmittingPhase(true)
      try {
        const payload = {
          projectName: project?.project_name,
          period: currentPeriod,
          phaseId: selectedPhase?.id,
          section: 'contractVOs',
        }
        const res: Record<string, any> = await updateOverallPhaseWithMultiPhaseApi(payload)
        if (res.payload.success) {
          successToast(res?.payload?.message)
          fetchData()
        } else {
          errorToast(res.payload.response.data.message || 'Failed to update phase')
        }
      } catch (error) {
        errorToast('Failed to update overall multi phase')
      } finally {
        setIsSubmittingPhase(false)
      }
    }
  }

  const handleDelete = async () => {
    setShowDeleteConfirmation(true)
    setDeleteId(commercialData?.id)
    // if (commercialData?.id) {
    //   const response: Record<string, any> = await deleteCommercialApi(
    //     commercialData?.id
    //   );
    //   if (response.payload.success) {
    //     const response: Record<string, any> = await getCommercialApi({
    //       period: currentPeriod,
    //     });
    //     if (response.payload.success) {
    //       const filterCommercial: any = response.payload.data.find(
    //         (item: any) => {
    //           return item.project_name === router.query.slug;
    //         }
    //       );
    //       setCommercialData(filterCommercial);
    //     }
    //   }
    // }
  }

  const handleToggleAll = (e: any) => {
    e.stopPropagation()
    const isAllExpanded = contractVosAccordion.every((item) => item.isAccordion)
    const updated = contracts.map((_: any, index: number) => ({
      index,
      isAccordion: !isAllExpanded, // collapse if all expanded, else expand
    }))
    setContractVosAccordion(updated)
  }

  useEffect(() => {
    const bottomElement = document.getElementById('bottom-element')
    if (bottomElement && addState) {
      bottomElement.scrollIntoView({
        behavior: 'smooth',
      })
      setAddSetTime(false)
    }
  }, [contracts, addState])

  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  const groupedContracts = useMemo(() => {
    const result: Record<string, Record<string, any[]>> = {}

    contracts.forEach((item: any) => {
      const phase =
        Array.isArray(item?.LookupProjectToPhase) && item.LookupProjectToPhase.length > 0
          ? item.LookupProjectToPhase[0]?.phase
          : '-'
      const type = item?.MasterContractType?.contract_type || 'No Contract Type'

      if (!result[phase]) result[phase] = {}
      if (!result[phase][type]) result[phase][type] = []
      result[phase][type].push(item)
    })

    return result
  }, [contracts])

  return (
    <div className={styles.mainContainer}>
      {currentUser.role?.view_permissions.includes('Commercials-Project Summary') && (
        <div className={styles.accordionSection}>
          <PulseAccordion
            expanded={expandedBasicDetails} // Allow multiple expansions
            onChange={() => setExpandedBasicDetails(!expandedBasicDetails)}
            summaryContent={
              <CommercialHeader
                isEditMode={isEditMode}
                isEditForUser={isEditForUser}
                setIsEditMode={setIsEditMode}
                formik={formik}
                handleDelete={handleDelete}
                commercialData={commercialData}
              />
            }
            detailsContent={
              <div className={styles.container}>
                <div className={styles.contentWrapper}>
                  <div className={styles.header}>
                    {/* {commercialDataProjectSummary?.description ? commercialDataProjectSummary?.description : null} */}
                  </div>
                  <CommercialFields formik={formik} isEditMode={isEditMode} loader={loader} />
                </div>
              </div>
            }
            className={styles.accordion}
            summaryClassName={styles.content}
            detailsClassName={styles.details}
          />
        </div>
      )}

      {currentUser.role?.view_permissions.includes('Commercials-Contract & VOs') && (
        <div className={styles.accordionSection}>
          <PulseAccordion
            expanded={expanded} // Allow multiple expansions
            onChange={() => setExpanded(!expanded)}
            summaryContent={
              <ContractVosHeader
                isEditForUser={isEditForUser}
                contracts={contracts}
                setAddSetTime={setAddSetTime}
                setContracts={setContracts}
                expanded={expanded}
                contractVosAccordion={contractVosAccordion}
                handleToggleAll={handleToggleAll}
              />
            }
            detailsContent={
              <>
                {loader ? (
                  <Loader />
                ) : (
                  <div className={styles.accordionContainer}>
                    {Object.entries(groupedContracts).map(([phaseName, contractTypes], phaseIndex) => {
                      const isPhaseAccordionOpen = contractVosAccordion.some(
                        (item: any) => item.index === `phase-${phaseIndex}` && item.isAccordion,
                      )

                      const handlePhaseAccordion = () => {
                        const isOpen = contractVosAccordion.some(
                          (item: any) => item.index === `phase-${phaseIndex}` && item.isAccordion,
                        )

                        if (isOpen) {
                          setContractVosAccordion([])
                        } else {
                          setContractVosAccordion([{ index: `phase-${phaseIndex}`, isAccordion: true }])
                        }
                      }

                      return (
                        <div className={stylesAccordion.container} key={`phase-${phaseIndex}`} id={phaseName}>
                          <Accordion
                            expanded={isPhaseAccordionOpen}
                            onChange={handlePhaseAccordion}
                            classes={{ root: stylesAccordion.accordion }}
                          >
                            <AccordionSummary
                              expandIcon={<DropDownIcon className={stylesAccordion.expandIcon} />}
                              className={stylesAccordion.summary}
                              classes={{ content: stylesAccordion.content }}
                            >
                              <div className={stylesAccordion.summaryTitle}>
                                <div className={stylesAccordion.header}>{phaseName}</div>
                              </div>
                            </AccordionSummary>
                            <AccordionDetails className={stylesAccordion.details}>
                              {Object.entries(contractTypes).map(([contractType, contract], typeIndex) => (
                                <ContractAndVos
                                  key={typeIndex}
                                  contract={[contractType, contract]}
                                  index={typeIndex}
                                  id={`${phaseName}-${contractType}`}
                                  contractVosAccordion={contractVosAccordion}
                                  setContractVosAccordion={setContractVosAccordion}
                                  contracts={contracts}
                                  setContracts={setContracts}
                                  phase={phaseName}
                                />
                              ))}
                            </AccordionDetails>
                          </Accordion>
                        </div>
                      )
                    })}
                  </div>
                )}
              </>
            }
            className={styles.accordion}
            summaryClassName={styles.content}
            detailsClassName={styles.details}
          />
        </div>
      )}

      {/* <Drawer anchor="right" open={isOpenDrawer} onClose={handleCloseDrawer}>
        <AddCommercialDrawer
          onClose={handleCloseDrawer}
          onAddCommercial={handleAddCommercial}
          commercial={selectedCommercial}
          onUpdateCommercial={handleUpdateCommercial}
        />
      </Drawer> */}

      <ConfirmDeleteModal
        open={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        handleConfirm={() => {
          handleDeleteAction(deleteModel as number)
          setShowDeleteConfirmation(false)
        }}
      />
    </div>
  )
}

export default CommercialsTab

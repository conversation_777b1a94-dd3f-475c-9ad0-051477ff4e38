import React, { useState } from 'react'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import Head from 'next/head'
import Image from 'next/image'
import styles from './login.module.scss'
import aldarLogo from '../../public/svg/aldarLogo.svg'
import Button from '@/src/component/shared/button'
import { StorageKeys } from '@/src/constant/enum'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import { removeLocalStorageItem } from '@/src/utils/storageUtils'

const Login = () => {
  const { authUrlApi } = useAuthorization()
  const { setRecentProject } = useProjectSummary()

  const [toastDisplay, setToastDisplay] = useState(false)

  const handleLogin = async () => {
    removeLocalStorageItem(StorageKeys.IS_LOGIN)
    setRecentProject({ projectName: '' })
    const redirectUrl = `${window.location.origin}/redirect`
    const res: Record<string, any> = await authUrlApi(redirectUrl)
    if (res?.payload.exitCode === 138) {
      setToastDisplay(true)
    } else {
      if (!res.payload.success) return
      window.location.href = res.payload.data
      // router.replace(res.payload.data);`
    }
  }

  return (
    <div className={styles.container}>
      <Head>
        <title>Login</title>
        <meta name="description" content="Login" />
      </Head>
      <div className={`${styles.card} ${styles.overlay}`}>
        <div className={styles.contain}>
          <div className={styles.fields}>
            <Image src={aldarLogo} height={100} width={100} alt="aldar" />
          </div>
          <h1>Welcome to PULSE</h1>
          <Button color="secondary" onClick={() => handleLogin()} className={styles.button}>
            Login
          </Button>

          {toastDisplay && (
            <>
              <p className={styles.accessDeiedMsg}>
                Access to this application is not authorized for you. Please request access to this application.
              </p>
              <div className={styles.requestAccessSection}>
                <div className={styles.requestAccessContent}>
                  <div className={styles.requestAccessLinks}>
                    <a
                      href="https://sd.aldar.com/app/itdesk/ui/requests/add?reqTemplate=149471000074670023"
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`${styles.requestAccessLink} ${styles.portalLink}`}
                      aria-label="Visit ALDAR IT Service Desk Portal"
                    >
                      Request Access
                      <ArrowForwardIcon />
                    </a>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default Login

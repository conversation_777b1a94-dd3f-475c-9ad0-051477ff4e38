import { cleanNumber, isSameCombination, numberWithPrecision } from './validationError/weightageValidation'
import { MULTI_SELECT_SEPARATOR } from '@/src/constant/stageStatus'
import { errorToast } from '@/src/utils/toastUtils'

export const uniqueErrors = (errors: any[]) => {
  const filteredErrors = errors.filter((error) => error !== null)

  const uniqueRecords = filteredErrors.reduce((acc, item) => {
    // Check if the error item is already in the accumulator
    const exists = acc.find(
      (record: any) =>
        record.id === item.id &&
        (record.phase_weightage_field === item.phase_weightage_field ||
          record.design_stage_weightage_field === item.design_stage_weightage_field),
    )

    // If it doesn't exist, add it to the accumulator
    if (!exists) {
      acc.push(item)
    }

    return acc // Return the updated accumulator
  }, [])

  return uniqueRecords // Return the unique error records
}

const roundToDecimal = (value: number, decimalPlaces: number) => {
  const factor = Math.pow(10, decimalPlaces)
  return Math.round(value * factor) / factor
}

export const filterDifferentPhaseSameStageStatus = (records: any[], currentItem: any): any[] => {
  return records.filter(
    (item) =>
      !(
        isSameCombination(item.phase, currentItem.phase) &&
        isSameCombination(item.project_phase_category, currentItem.project_phase_category)
      ) &&
      item?.MasterProjectStageStatus?.project_stage_status ===
        currentItem?.MasterProjectStageStatus?.project_stage_status,
  )
}
export const filterDiffSubStageStatusDesignWeightage = (records: any[], currentItem: any): any[] => {
  return records.filter(
    (item) =>
      isSameCombination(item.phase, currentItem.phase) &&
      isSameCombination(item.project_phase_category, currentItem.project_phase_category) &&
      item?.MasterProjectStageStatus?.project_stage_status ===
        currentItem?.MasterProjectStageStatus?.project_stage_status &&
      item?.MasterProjectSubStage?.project_sub_stage !== currentItem?.MasterProjectSubStage?.project_sub_stage,
  )
}

export const filterSamePhaseSameStageStatus = (records: any[], currentItem: any): any[] => {
  return records.filter(
    (item) =>
      item?.phase === currentItem?.phase &&
      item?.MasterProjectStageStatus?.project_stage_status ===
        currentItem?.MasterProjectStageStatus?.project_stage_status,
  )
}

// export const filterSamePhaseSameStageStatus = (records: any[], currentItem: any): any[] => {
//   return records.filter((item) => {
//     return item?.stage_status === currentItem?.stage_status
//   })
// }

export const runValidation = async (masterPhase: any) => {
  const allErrors: any[] = [] // Array to hold all errors encountered during validation

  // Sort function
  const sort = (records: any[]) =>
    records.sort((a, b) => a.project_status_sorting_order - b.project_status_sorting_order)

  const sortedStatusData = sort(masterPhase)
  const grouped = groupBy(sortedStatusData, 'stage_status')
  const uniqueGrouped = filterUniqueCombinationsWithOrderInsensitive(grouped)

  // Iterate over each stage_status group in uniqueGrouped
  Object.entries(uniqueGrouped).forEach(([stageStatus, records]) => {
    // Calculate total phase_weightage for this group
    const totalPhaseWeightage = records.reduce(
      (sum, item) => sum + (item.phase_weightage ? Number(item.phase_weightage) * 100 : 0),
      0,
    )
    const totalSum = cleanNumber(totalPhaseWeightage)

    if (totalSum > 100) {
      records.forEach((item) => {
        const errorData: any = {
          id: item?.id,
          sorting_order: item?.project_status_sorting_order,
          phase: item?.phase || '-',
          stage_status: item?.MasterProjectStageStatus?.project_stage_status,
          sub_stage: item?.MasterProjectSubStage?.project_sub_stage,
          phase_weightage_field: 'Phase Weightage',
          phase_weightage_error: `exceeds 100.`,
        }
        allErrors.push(errorData)
      })
    } else if (totalSum < 100) {
      records.forEach((item) => {
        const errorData: any = {
          id: item?.id,
          sorting_order: item?.project_status_sorting_order,
          phase: item?.phase || '-',
          stage_status: item?.MasterProjectStageStatus?.project_stage_status,
          sub_stage: item?.MasterProjectSubStage?.project_sub_stage,
          phase_weightage_field: 'Phase Weightage',
          phase_weightage_error: `is below 100.`,
        }
        allErrors.push(errorData)
      })
    }
  })

  // Separate iteration for grouped records only for Design stage
  if (grouped['Design'] || grouped['design']) {
    const designRecords = grouped['Design'] || grouped['design']
    // Group by unique combination of category + phase
    const subGroups: any[] = []
    designRecords.forEach((item: any) => {
      let found = false
      for (const group of subGroups) {
        if (
          isSameCombination(group[0].project_phase_category, item.project_phase_category) &&
          isSameCombination(group[0].phase, item.phase)
        ) {
          group.push(item)
          found = true
          break
        }
      }
      if (!found) subGroups.push([item])
    })

    // Validate each sub-group
    subGroups.forEach((group: any[]) => {
      const totalDesignWeightage = group.reduce(
        (sum: number, item: any) => sum + (item.design_stage_weightage ? Number(item.design_stage_weightage) * 100 : 0),
        0,
      )
      const totalSum = cleanNumber(totalDesignWeightage)

      if (totalSum > 100) {
        group.forEach((item: any) => {
          allErrors.push({
            id: item?.id,
            sorting_order: item?.project_status_sorting_order,
            phase: item?.phase || '-',
            stage_status: item?.MasterProjectStageStatus?.project_stage_status,
            sub_stage: item?.MasterProjectSubStage?.project_sub_stage,
            design_stage_weightage_field: 'Design Stage Weightage',
            design_stage_weightage_error: `exceeds 100.`,
          })
        })
      } else if (totalSum < 100) {
        group.forEach((item: any) => {
          allErrors.push({
            id: item?.id,
            sorting_order: item?.project_status_sorting_order,
            phase: item?.phase || '-',
            stage_status: item?.MasterProjectStageStatus?.project_stage_status,
            sub_stage: item?.MasterProjectSubStage?.project_sub_stage,
            design_stage_weightage_field: 'Design Stage Weightage',
            design_stage_weightage_error: `is below 100.`,
          })
        })
      }
    })
  }

  // Get the unique errors from all encountered errors
  console.log('allErrors: ', allErrors)
  const uniqueErrorsArray = uniqueErrors(allErrors).flat()
  console.log('uniqueErrorsArray: ', uniqueErrorsArray)

  // Set the validation model with the collected unique errors
  return uniqueErrorsArray
}

export const dragAndDrop = async (
  statuses: any[],
  filteredStatuses: any[],
  currentPeriod: string,
  dragId: string,
  dropId: string,
  setLoading: (loading: boolean) => void,
  statusSortingApi: any,
  getStatusApi: any,
  projectName?: any,
) => {
  setLoading(true)

  const dragItem = statuses.find((item: any) => item.id.toString() === dragId.toString())
  const dropItem = statuses.find((item: any) => item.id.toString() === dropId.toString())

  if (!dragItem || !dropItem) {
    setLoading(false)
    return
  }

  const updatedDragItem = { ...dragItem }
  const updatedDropItem = { ...dropItem }

  // Remove unnecessary properties
  delete updatedDragItem.id
  delete updatedDragItem.last_update
  delete updatedDragItem.updated_by
  delete updatedDragItem.key_highlights_updated_by
  delete updatedDropItem.id
  delete updatedDropItem.last_update
  delete updatedDropItem.updated_by
  delete updatedDropItem.key_highlights_updated_by

  const min = Math.min(updatedDragItem.project_status_sorting_order, updatedDropItem.project_status_sorting_order)
  const max = Math.max(updatedDragItem.project_status_sorting_order, updatedDropItem.project_status_sorting_order)

  const isBetween = (value: any) => value >= min && value <= max

  const filteredAndSortedData = filteredStatuses
    .filter((item) => isBetween(item.project_status_sorting_order))
    .sort((a: any, b: any) => a.project_status_sorting_order - b.project_status_sorting_order)

  const findFirstValue = filteredAndSortedData.find(
    (item) => item.project_status_sorting_order === updatedDragItem.project_status_sorting_order,
  )
  const findFirsPlace = filteredAndSortedData.findIndex(
    (item) => item.project_status_sorting_order === updatedDropItem.project_status_sorting_order,
  )

  let arrangeData: any = [...filteredAndSortedData]

  // Reorder the array by placing the dragged item at the new position
  if (findFirstValue && findFirsPlace !== -1) {
    arrangeData = arrangeData.filter(
      (item: any) => item.project_status_sorting_order !== updatedDragItem.project_status_sorting_order,
    )
    arrangeData.splice(findFirsPlace, 0, findFirstValue)
  }

  const reOrderData = arrangeData.map((item: any, index: number) => ({
    ...item,
    project_status_sorting_order: min + index,
  }))

  const newPayload = reOrderData.map((item: any) => ({
    id: item.id.toString(),
    project_status_sorting_order: item.project_status_sorting_order,
  }))

  const res: any = await statusSortingApi({ period: currentPeriod, projectStatuses: newPayload })

  if (res?.payload.success) {
    await getStatusApi({ period: currentPeriod, project_name: projectName })
  }

  setLoading(false)
}

export const deleteStatusRecord = async (
  row: any,
  projectManagements: any[],
  currentPeriod: string,
  setDeleteModel: (model: any) => void,
  deleteProjectManagementsApi: any,
  deleteStatusApi: any,
  getStatusApi: any,
  setDeleteLoading?: (loading: boolean) => void,
) => {
  setDeleteLoading && setDeleteLoading(true)
  const res: any = await deleteStatusApi(row.id)

  if (res.payload.success === true) {
    await getStatusApi({
      period: currentPeriod,
      project_name: row?.project_name,
    })
  } else {
    errorToast(res?.payload?.response?.data?.message)
  }

  const projectManagement = projectManagements.filter((item) => {
    return item.project_name === row?.project_name
  })

  if (!projectManagement?.length) {
    setDeleteModel(null)
    setDeleteLoading && setDeleteLoading(false)
    console.log('delete done without project management')
    return
  }

  let data: any = {}
  projectManagement.forEach((item: any) => {
    const phase = row?.phase
    // TODO: Need to check
    const stageStatus = row?.stage_status
    const subStage = row?.sub_stage
    const hasPhaseValue = phase && item.phase === phase
    const hasStageStatus = item.stage_status === stageStatus
    // TODO : Need to check
    const hasSubStage = item.sub_stage === subStage

    if (hasPhaseValue) {
      if (stageStatus === 'Design') {
        if (item.sub_stage === subStage) {
          data = item
        }
      } else if (hasStageStatus) {
        data = item
      }
    }
  })

  if (data?.id) {
    await deleteProjectManagementsApi(data?.id)
  }

  setDeleteLoading && setDeleteLoading(false)
  setDeleteModel(null)
  console.log('delete done')
}

const joinMultiSelectFieldValue = (value: string | string[]) => {
  if (typeof value === 'string') {
    return value
  }
  return value.join(MULTI_SELECT_SEPARATOR)
}

export const handleStageStatusSubmit = async (
  values: any,
  editData: any,
  setEditData: any,
  currentPeriod: string,
  router: any,
  setLoading: (loading: boolean) => void,
  formik: any,
  statusData: any[],
  updateStatusApi: (payload: any) => Promise<any>,
  addStatusApi: (payload: any) => Promise<any>,
  getStatusApi: (params: { period: string; project_name?: string }) => Promise<any>,
) => {
  setLoading(true) // Set loading to true before the API call
  try {
    const payload: any = {
      period: currentPeriod,
      project_name: router.query.slug,
      project_to_project_phase_ids: values?.project_to_project_phase_ids?.length
        ? values?.project_to_project_phase_ids
        : null,
      project_to_project_phase_category_ids: values?.project_to_project_phase_category_ids?.length
        ? values?.project_to_project_phase_category_ids
        : null,
      master_project_stage_status_id: values?.master_project_stage_status_id,
      master_project_phase_category_id: values?.master_project_phase_category_id,
      /**
       *  TODO: This is set temporary for testing with single category 
       * Curuntly remove this
      /* 
        values?.master_project_phase_category_id?.length <= 1
          ? (Number(values?.master_project_phase_category_id) ?? null)
          : joinMultiSelectFieldValue(values?.master_project_phase_category_id), 
          */
      phase_weightage:
        values?.phaseWeightage !== '' && values?.phaseWeightage !== null
          ? numberWithPrecision(values?.phaseWeightage)
          : null, // Ensures 0 is passed when applicable
      last_updated: new Date().toISOString(),
      predecessor_ids: values?.predecessor,
      successor_ids: values?.successor,
    }

    if ('master_project_sub_stage_id' in values) {
      payload.master_project_sub_stage_id = values.master_project_sub_stage_id
        ? values?.master_project_sub_stage_id
        : null
    }

    if ('designStageWeightage' in values) {
      payload.design_stage_weightage =
        values?.designStageWeightage !== '' && values?.designStageWeightage !== null
          ? numberWithPrecision(values?.designStageWeightage)
          : null // Ensures 0 is passed when applicable
    }

    if (payload.master_project_stage_status_id !== '3') {
      payload.design_stage_weightage = null
      payload.master_project_sub_stage_id = null
    }

    let res: Record<string, any>
    if (editData) {
      res = await updateStatusApi({
        id: editData,
        data: payload,
      })

      if (res.payload.success === true) {
        const getData: Record<string, any> = await getStatusApi({
          period: currentPeriod,
          project_name: router?.query?.slug as string,
        })
        if (getData?.payload?.success) {
          setLoading(false)
        }
      } else {
        errorToast(res?.payload?.response?.data?.message || 'Failed')
      }
    } else {
      payload.project_status_sorting_order = statusData.length ? statusData[statusData.length - 1].sortingOrder + 1 : 1

      res = await addStatusApi(payload)

      if (res.payload.success === true) {
        const getData: Record<string, any> = await getStatusApi({
          period: currentPeriod,
          project_name: router?.query?.slug as string,
        })
        if (getData?.payload?.success) {
          setLoading(false)
        }
      } else {
        errorToast(res?.payload?.response?.data?.message || 'Failed')
      }
    }
  } catch (error) {
    console.error(error)
  } finally {
    formik.resetForm()
    setEditData(null)
    setLoading(false)
  }
}

/**
 * Groups an array of objects by the specified key.
 * @param array The array to group.
 * @param key The key to group by.
 * @returns An object with keys as unique values of the specified key, and values as arrays of objects.
 */
export function groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
  return array.reduce(
    (result, item) => {
      const groupKey = String(item[key] ?? 'undefined')
      if (!result[groupKey]) {
        result[groupKey] = []
      }
      result[groupKey].push(item)
      return result
    },
    {} as Record<string, T[]>,
  )
}

/**
 * Filters unique records in each group based on combination of project_phase_category + phase + stage_status,
 * using isSameCombination for phase and category.
 * @param grouped The grouped object from groupBy.
 * @returns The grouped object with unique records in each array.
 */
export function filterUniqueCombinationsWithOrderInsensitive(grouped: Record<string, any[]>): Record<string, any[]> {
  const result: Record<string, any[]> = {}
  Object.entries(grouped).forEach(([stageStatus, records]) => {
    const unique: any[] = []
    records.forEach((item) => {
      const exists = unique.some(
        (u) =>
          isSameCombination(u.project_phase_category, item.project_phase_category) &&
          isSameCombination(u.phase, item.phase) &&
          u.stage_status === item.stage_status,
      )
      if (!exists) unique.push(item)
    })
    result[stageStatus] = unique
  })
  return result
}

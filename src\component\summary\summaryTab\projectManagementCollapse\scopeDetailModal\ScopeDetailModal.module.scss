@import '/styles/color.scss';

.header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  align-items: flex-start;
  > p {
    font-size: 18px;
    font-weight: 900;
    padding-top: 5px;
  }
}

.tableContainer {
  padding-top: 20px;
  padding-bottom: 20px;
  //   .addBtn {
  //     display: flex;
  //     flex-direction: row-reverse;
  //     padding-bottom: 10px;
  //   }
}

.editBtnContainer {
  display: flex;
  gap: 10px;
}

.closeIconBtn {
  display: flex;
  justify-content: flex-end;
  margin-top: -10px;
  padding-bottom: 10px;
}

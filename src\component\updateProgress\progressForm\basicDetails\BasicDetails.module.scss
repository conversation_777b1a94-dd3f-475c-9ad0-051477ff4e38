@import '/styles/color.scss';

.endAdornment {
  margin-right: 0px !important;
  .endAdornmentIcon {
    text-align: center;
    color: $BLACK;
  }
}

.datePicker {
  > label {
    font-family: Poppins !important;
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 18px !important;
    color: #808080 !important;
  }
}

.greenHighlight {
  div {
    background-color: #f2f2f2 !important;
  }
  > div {
    // border: 1px solid #0aa000;
    > div > input {
      color: #0aa000 !important;
    }
  }
}

.inputFields > div > div {
  background-color: #f2f2f2 !important;
  border: 1px solid #f2f2f2;
}

.dataPickerInput {
  div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2;
  }
}

.planPercentage > div {
  background-color: #f2f2f2 !important;
  border: 1px solid #f2f2f2;
}

.highlightField {
  div > div {
    border: 1px solid rgb(40 101 220) !important;
    background-color: #f0f8ff !important;

    > div {
      border: none !important;
    }
  }
}

// .comboHighlight {
//   > div {
//     border: 1px solid rgb(40 101 220);
//     background-color: #f0f8ff !important;
//     border-radius: 3px;
//   }
// }

.comboHighlight {
  > div {
    border: 1px solid rgb(40 101 220) !important;
    border-radius: 3px;
    > div {
      background-color: #f0f8ff !important;
    }
  }
}
.comboBoxField {
  > div > div {
    background-color: #f2f2f2 !important;
    border: 1px solid #f2f2f2 !important;
  }
}
.checkbox {
  padding: 0px !important;
  padding-top: 3px !important;
}

.fieldContainer {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 24px;
  margin-top: 16px;
}

.lineSeparator {
  width: 100%;
  height: 1px;
  background-color: #e0e0e0;
  margin-top: 16px;
}

.baseLineCheckbox {
  margin-top: 10px;
  background-color: #f0f8ff !important; // light blue or any color you prefer
  border: 1px solid #2865dc !important;
  border-radius: 3px;

  > span {
    padding: 0 !important;
    border-radius: 3px;
  }
}

.textEditorContainer {
  .title {
    margin-bottom: 4px;
    font-family: Poppins;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: #808080;
  }
  .textEditor {
    background-color: #f2f2f2;
  }
}

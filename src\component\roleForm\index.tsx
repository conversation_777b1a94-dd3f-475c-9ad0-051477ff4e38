import React, { useEffect, useState } from 'react'
import { InputAdornment, Tooltip } from '@mui/material'
import { separateEntitiesAndProjects, toCapitalizedFormat } from './helper'
import ItemList from './itemList'
import styles from './RoleForm.module.scss'
import ModelPopOver from './SelectPopOver'
import { useRoleForm } from './useRoleForm'
import EyeIcon from '../svgImages/eyeIcon'
import SearchIcon from '../svgImages/searchIcon'
import Button from '@/src/component/shared/button'
import Checkbox from '@/src/component/shared/checkbox'
import TextInputField from '@/src/component/shared/textInputField'
import Typo<PERSON><PERSON>ield from '@/src/component/shared/typography'
import LeftArrow from '@/src/component/svgImages/leftArrow'
import { permissionList } from '@/src/constant/enum'
import { convertMultiSelectOption } from '@/src/utils/arrayUtils'

const RoleForm: React.FC = () => {
  const {
    loading,
    formik,
    editRole,
    permissions,
    projectAndEntities,
    selectedPermissionType,
    navigateToUserManagement,
    handlePermissionChange,
    handlePermissionTypeChange,
  } = useRoleForm()
  const [searchTerm, setSearchTerm] = useState('')
  const [itemList, setItemList] = useState(false)
  const [entity, setEntity] = useState(
    convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).entities),
  )
  const [project, setProject] = useState(
    convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).projects),
  )
  const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null)
  const [isModel, setIsModel] = useState(false)
  const [modalContent, setModalContent] = useState('')
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const resetSearch = () => {
    setSearchTerm('')
    setEntity(convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).entities))
    setProject(convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).projects))
  }

  useEffect(() => {
    setEntity(convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).entities))
    setProject(convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).projects))
  }, [projectAndEntities])

  useEffect(() => {
    if (modalContent.toLowerCase() === 'entity') {
      setSelectedItems(formik.values.selectedEntities)
    } else if (modalContent.toLowerCase() === 'projects') {
      setSelectedItems(formik.values.selectedProjects)
    }
  }, [formik.values.selectedEntities, formik.values.selectedProjects, modalContent])

  useEffect(() => {
    // Update selected items when modal content changes
    if (isModel) {
      setSelectedItems(
        modalContent.toLowerCase() === 'entity' ? formik.values.selectedEntities : formik.values.selectedProjects,
      )
    }
  }, [isModel])

  const summarySections = permissions.filter(
    (section: any) => section.startsWith('Summary') || section.startsWith('Governance'),
  )

  const statusSections = permissions.filter((section: any) => section.startsWith('Status'))
  const useEditFreezePermission = permissions.filter((section: any) =>
    section.startsWith('Update Over User Freeze Period'),
  )
  const progressEditPermission = permissions.filter((section: any) => section.startsWith('Progress Edit Permission'))
  const historicalPermission = permissions.filter((section: any) =>
    section.startsWith('Navigate To Historical Periods'),
  )
  const editLeadPermission = permissions.filter((section: any) => section.startsWith('Edit Lead Functionality'))
  const spaEditPermission = permissions.filter((section: any) => section.startsWith('SPA & Milestones Edit Permission'))
  const projectSorting = permissions.filter((section: any) => section.startsWith('Project Sorting'))
  const executiveSorting = permissions.filter((section: any) => section.startsWith('Executive Sorting'))
  const keyAcheivementSorting = permissions.filter((section: any) => section.startsWith('Key Achievements Sorting'))
  const commercialSection = permissions.filter((section: any) => section.startsWith('Commercials'))
  const mediaSection = permissions.filter((section: any) => section.startsWith('Media'))
  const batchUploadSection = permissions.filter((section: any) => section.startsWith('Batch'))
  const updateForecastDateBtn = permissions.filter((section: any) => section.startsWith('Edit Forecast Finish Date'))
  const updateBaseLinFinishAndPlanDurationDate = permissions.filter((section: any) =>
    section.startsWith('Edit Baseline Plan Finish Date And Plan Duration'),
  )

  const otherSections = permissions.filter(
    (section) =>
      !summarySections.includes(section) &&
      !statusSections.includes(section) &&
      !projectSorting.includes(section) &&
      !executiveSorting.includes(section) &&
      !keyAcheivementSorting.includes(section) &&
      !useEditFreezePermission.includes(section) &&
      !spaEditPermission.includes(section) &&
      !progressEditPermission.includes(section) &&
      !editLeadPermission.includes(section) &&
      !mediaSection.includes(section) &&
      !commercialSection.includes(section) &&
      !batchUploadSection.includes(section) &&
      !historicalPermission.includes(section) &&
      !updateBaseLinFinishAndPlanDurationDate.includes(section) &&
      !updateForecastDateBtn.includes(section),
  )

  const entityOption = convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).entities)
  const projectOption = convertMultiSelectOption(separateEntitiesAndProjects(projectAndEntities).projects)
  const allEntitiesSelected = selectedItems.length === entityOption.length
  const allProjectsSelected = selectedItems.length === projectOption.length

  const excludedPermissions = [
    'Project Sorting',
    'Progress Edit Permission',
    'Navigate To Historical Periods',
    'Edit Lead Functionality',
    'Key Achievements Sorting',
    'SPA & Milestones Edit Permission',
    'Update Over User Freeze Period',
    'Batch Upload',
  ]

  const getFilteredPermissions = (permissions: string[]) =>
    permissions.filter((permission) => !excludedPermissions.includes(permission))

  const getExcludedPermissions = (permissions: string[]) =>
    permissions.filter((permission) => excludedPermissions.includes(permission))

  const filteredPermissionsAllCheck = getFilteredPermissions(permissionList)
  const formikAllCheck = getFilteredPermissions(formik.values.permissions)
  const emptyValue = getExcludedPermissions(formik.values.permissions)

  const allPermissionsChecked = filteredPermissionsAllCheck.length === formikAllCheck.length

  const toggleAllPermissions = () => {
    const newPermissions = allPermissionsChecked ? emptyValue : filteredPermissionsAllCheck
    formik.setValues({ ...formik.values, permissions: [...newPermissions, ...getExcludedPermissions(permissions)] })
  }

  const toggleAllEntities = () => {
    const newEntities = allEntitiesSelected ? [] : entityOption.map((entity) => entity.id)
    setSelectedItems(newEntities)
  }

  const toggleAllProjects = () => {
    const newProjects = allProjectsSelected ? [] : projectOption.map((project) => project.id)
    setSelectedItems(newProjects)
  }

  const handleItemSelection = (id: string) => {
    setSelectedItems((prevSelectedItems) =>
      prevSelectedItems.includes(id) ? prevSelectedItems.filter((item) => item !== id) : [...prevSelectedItems, id],
    )
  }

  const handleSearch = (e: any) => {
    setSearchTerm(e.target.value)
    const entityFilter = entityOption.filter((item) => item.name.toLowerCase().includes(e.target.value.toLowerCase()))
    const projectFilter = projectOption.filter((item) => item.name.toLowerCase().includes(e.target.value.toLowerCase()))
    modalContent.toLowerCase() === 'entity' ? setEntity(entityFilter) : setProject(projectFilter)
  }

  return (
    <>
      {/* {loading ? (
        <Loader />
      ) : ( */}
      <div className={styles.container}>
        <div className={styles.headerTitle}>
          <TypographyField variant="h5" text={'User Management'} />
        </div>
        <div className={styles.header} onClick={navigateToUserManagement}>
          <LeftArrow className={styles.leftArrow} />
          <TypographyField variant="subheadingSemiBold" text="Add Role Template" className={styles.detailsText} />
        </div>
        <div className={styles.content}>
          <form className={styles.form} onSubmit={formik.handleSubmit}>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '24px' }}>
              <TextInputField
                className={styles.textField}
                name="role_name"
                labelText={'Role Name'}
                placeholder="Type Something..."
                variant={'outlined'}
                value={formik.values.role_name}
                onChange={formik.handleChange}
              />
            </div>
            <div className={styles.permissionContainer}>
              <div>
                <TypographyField text={'View Permission'} style={{ color: '#808080', fontSize: '12px' }} />
                <div>
                  <div key={'selectAll'} className={`${styles.checkbox} `}>
                    <Checkbox checked={allPermissionsChecked} onChange={toggleAllPermissions} />
                    <TypographyField text={'Select All'} style={{ fontSize: '12px' }} />
                  </div>

                  {summarySections.map((permission: string, index: number) => (
                    <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                      <Checkbox
                        checked={formik.values.permissions.includes(permission)}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <TypographyField text={permission} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                  {statusSections.map((permission: string, index: number) => (
                    <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                      <Checkbox
                        checked={formik.values.permissions.includes(permission)}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <TypographyField text={permission} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                  {mediaSection.map((permission: string, index: number) => (
                    <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                      <Checkbox
                        checked={formik.values.permissions.includes(permission)}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <TypographyField text={permission} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                  {commercialSection.map((permission: string, index: number) => (
                    <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                      <Checkbox
                        checked={formik.values.permissions.includes(permission)}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <TypographyField text={permission} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                  {otherSections.map((permission: string, index: number) => (
                    <div key={permission} className={`${styles.checkbox} `}>
                      <Checkbox
                        checked={formik.values.permissions.includes(permission)}
                        onChange={() => handlePermissionChange(permission)}
                      />
                      <TypographyField text={permission} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <TypographyField text={'Edit Permission'} style={{ color: '#808080', fontSize: '12px' }} />

                {executiveSorting.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {projectSorting.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {useEditFreezePermission.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {progressEditPermission.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {historicalPermission.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {editLeadPermission.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {keyAcheivementSorting.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {spaEditPermission.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {batchUploadSection.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {updateForecastDateBtn.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
                {updateBaseLinFinishAndPlanDurationDate.map((permission: string, index: number) => (
                  <div key={permission} className={`${styles.checkbox} ${index !== 0 && styles.mainPermissions}`}>
                    <Checkbox
                      checked={formik.values.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    />
                    <TypographyField text={permission} style={{ fontSize: '12px' }} />
                  </div>
                ))}
              </div>

              <div className={styles.permissionTypeWrapper}>
                <div>
                  <TypographyField text={'Permissions Type'} style={{ color: '#808080', fontSize: '12px' }} />
                  <div>
                    {['entity', 'projects'].map((permissionType: string) => (
                      <div key={permissionType} className={styles.checkbox}>
                        <Checkbox
                          checked={formik.values.permission_type.includes(permissionType)}
                          onChange={() => handlePermissionTypeChange(permissionType)}
                        />
                        <TypographyField text={toCapitalizedFormat(permissionType)} style={{ fontSize: '12px' }} />
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div>
                {selectedPermissionType === 'entity' && (
                  <div className={styles.buttonsOfSelect}>
                    <Button
                      onClick={() => {
                        setIsModel(true)
                        setModalContent('Entity')
                      }}
                    >
                      Select Entities
                    </Button>
                    {formik.values.selectedEntities.length ? (
                      <Tooltip title="View Selected Entities" arrow>
                        <EyeIcon
                          className={styles.eyeIcon}
                          onClick={(e) => {
                            setItemList(true)
                            setAnchorEl(e.currentTarget)
                          }}
                        />
                      </Tooltip>
                    ) : (
                      ''
                    )}
                  </div>
                )}

                {selectedPermissionType === 'projects' && (
                  <div className={styles.buttonsOfSelect}>
                    <Button
                      className={styles.eyeIcon}
                      onClick={() => {
                        setIsModel(true)
                        setModalContent('Projects')
                      }}
                    >
                      Select Projects
                    </Button>
                    {formik.values.selectedProjects.length ? (
                      <Tooltip title="View Selected Projects" arrow>
                        <EyeIcon
                          className={styles.eyeIcon}
                          onClick={(e) => {
                            setItemList(true)
                            setAnchorEl(e.currentTarget)
                          }}
                        />
                      </Tooltip>
                    ) : (
                      ''
                    )}
                  </div>
                )}
              </div>
            </div>
            <Button
              type="submit"
              className={styles.addUserButton}
              disabled={!formik.values.role_name?.trim() || !formik.values.permission_type?.length}
            >
              + {editRole ? 'Update' : 'Add'} Role Template
            </Button>
          </form>
        </div>
        <ModelPopOver
          open={isModel}
          onClose={() => {
            setSelectedItems([])
            setIsModel(false)
            resetSearch()
          }}
          onSubmit={() => {
            if (modalContent.toLowerCase() === 'entity') {
              formik.setFieldValue('selectedEntities', selectedItems)
            } else if (modalContent.toLowerCase() === 'projects') {
              formik.setFieldValue('selectedProjects', selectedItems)
            }
            setIsModel(false)
            resetSearch()
          }}
          title={modalContent.toLowerCase() === 'entity' ? 'Select Entities' : 'Select Projects'}
        >
          <div className={styles.contentWrapper}>
            <div className={styles.searchField}>
              <TextInputField
                // className={styles.searchField}
                placeholder="Search..."
                variant={'outlined'}
                value={searchTerm}
                onChange={(e) => {
                  handleSearch(e)
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="start" className={styles.endAdornment}>
                      <SearchIcon className={styles.endAdornmentIcon} />
                    </InputAdornment>
                  ),
                }}
              />
            </div>
            <div className={styles.contentOfModel}>
              {modalContent.toLowerCase() === 'entity' ? (
                <>
                  <div className={styles.checkbox}>
                    <Checkbox checked={allEntitiesSelected} onChange={toggleAllEntities} />
                    <TypographyField text="Select All" style={{ fontSize: '12px' }} />
                  </div>
                  {entity.map((item) => (
                    <div key={item.name} className={styles.checkbox}>
                      <Checkbox
                        checked={selectedItems.includes(item.id)}
                        onChange={() => handleItemSelection(item.id)}
                      />
                      <TypographyField text={item.name} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                </>
              ) : (
                <>
                  <div className={styles.checkbox}>
                    <Checkbox checked={allProjectsSelected} onChange={toggleAllProjects} />
                    <TypographyField text="Select All" style={{ fontSize: '12px' }} />
                  </div>
                  {project.map((item) => (
                    <div key={item.name} className={styles.checkbox}>
                      <Checkbox
                        checked={selectedItems.includes(item.id)}
                        onChange={() => handleItemSelection(item.id)}
                      />
                      <TypographyField text={item.name} style={{ fontSize: '12px' }} />
                    </div>
                  ))}
                </>
              )}
            </div>
          </div>
        </ModelPopOver>

        <ItemList
          open={itemList}
          anchorEl={anchorEl}
          onClose={() => {
            setItemList(false)
            setAnchorEl(null)
          }}
          items={
            selectedPermissionType === 'projects' ? formik.values.selectedProjects : formik.values.selectedEntities
          }
          itemName={selectedPermissionType === 'projects' ? 'Projects' : 'Entities'}
        />
      </div>
      {/* )} */}
    </>
  )
}

export default RoleForm

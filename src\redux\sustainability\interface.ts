import { StatusEnum } from '../types'

export interface ISustainabilityState {
  getSustainabilitiesStatus: StatusEnum
  addSustainabilityStatus: StatusEnum
  deleteSustainabilityStatus: StatusEnum
  updateSustainabilityStatus: StatusEnum
  sustainabilities: ISustainability[]
  sustainability: ISustainability
}
export interface ISustainability {
  id?: string
  period: string
  project_name: string
  waste_recycled_percentage: string
  reinvented_economy_percentage: string
  reinvented_economy_value: number
  pearl_rating_percentage: number
  recycled_material_percentage: number
  workers_welfare_compliance_percentage: string
  last_updated: string
  renewable_energy: string
  carbon_emissions: string
  emissions_per_m2: string
  number_of_grievances: string
}

export interface IGetSustainabilityResponse {
  data: ISustainability[]
  message: string
  success: true
}

import { isAfter, parse } from 'date-fns'
import { isDateBefore, isValidYMDDate } from '../dateUtils'
import { slippageJustificationValidation } from '@/src/component/summary/statusTab/statusService'
import {
  CONSTRUCTION,
  CONTRACTOR_PROCUREMENT,
  DESIGN,
  DLP_PROJECT_CLOSEOUT,
  INITIATION,
  LDC_PROCUREMENT,
  PREDECESSOR_SUCCESSOR_SEPARATOR,
} from '@/src/constant/stageStatus'
import { IStatus } from '@/src/redux/status/interface'
import { convertToPercentage } from '@/src/utils/numberUtils'

interface StageConfig {
  fieldsToCheck: string[]
  comparisonStage: string
  messageTemplate: string
}

export class ActualProgressValidation {
  private static isContractDlpConstruction = [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT]

  private static isInitiationDesignLdc = [LDC_PROCUREMENT, DESIG<PERSON>, INITIATION]

  private static parseDate(dateStr: string) {
    const yyyyMMddRegex = /^\d{4}-\d{2}-\d{2}$/

    // If date format is 'yyyy-MM-dd', parse accordingly
    if (yyyyMMddRegex.test(dateStr)) {
      return parse(dateStr, 'yyyy-MM-dd', new Date())
    }

    // Default to 'dd-MM-yyyy' format
    return parse(dateStr, 'dd-MM-yyyy', new Date())
  }

  /**
   * Checks if max date should be today based on the progress and stage status.
   * @param values - Form input values.
   * @param status - Status object.
   * @returns {Promise<boolean>} - True if max date should be today, false otherwise.
   */
  static async isMaxDateTodayIfValue100(values: any, status: IStatus): Promise<boolean> {
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status
    let actualPlanPercentage
    if ('actual_plan_percentage' in values) {
      actualPlanPercentage = values?.actual_plan_percentage
    } else {
      actualPlanPercentage = convertToPercentage(status?.actual_plan_percentage)
    }
    let actualPercentage
    if ('actual_percentage' in values) {
      actualPercentage = values?.actual_percentage
    } else {
      actualPercentage = convertToPercentage(status?.actual_percentage)
    }

    // Check if the stage is in 'Contractor, DLP, or Construction' status
    if (this.isContractDlpConstruction.includes(stage_status_name)) {
      const actualProgress = actualPlanPercentage // Using actual percentage for relevant stages
      let checkField
      if ('forecasted_end_date' in values) {
        checkField = values?.forecasted_end_date
      } else {
        checkField = status?.forecasted_end_date
      }
      if (checkField && Number(actualProgress) === 100) {
        const today = new Date()
        const date = this.parseDate(checkField)
        const isFutureDate = isAfter(date, today)
        return isFutureDate
      }
    }

    // Check if the stage is in 'Initiation, Design, or LDC Procurement' status
    if (this.isInitiationDesignLdc.includes(stage_status_name)) {
      const actualProgress = actualPercentage // Using actual plan percentage for relevant stages
      let checkField
      if ('forecast_finish' in values) {
        checkField = values?.forecast_finish
      } else {
        checkField = status?.forecast_finish
      }
      if (checkField && Number(actualProgress) === 100) {
        const today = new Date()
        const date = this.parseDate(checkField)
        const isFutureDate = isAfter(date, today)
        return isFutureDate
      }
    }

    return false // Default return value, update this as per your logic
  }

  static async isNotAllowedPastDateOrTodayDateIfValueIsLess100(values: any, status: IStatus): Promise<boolean> {
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status
    let actualPlanPercentage
    if ('actual_plan_percentage' in values) {
      actualPlanPercentage = values?.actual_plan_percentage
    } else {
      actualPlanPercentage = convertToPercentage(status?.actual_plan_percentage)
    }
    let actualPercentage
    if ('actual_percentage' in values) {
      actualPercentage = values?.actual_percentage
    } else {
      actualPercentage = convertToPercentage(status?.actual_percentage)
    }
    // Check if the stage is in 'Contractor, DLP, or Construction' status
    if (this.isContractDlpConstruction.includes(stage_status_name)) {
      const actualProgress = actualPlanPercentage // Using actual percentage for relevant stages
      let checkField
      if ('forecasted_end_date' in values) {
        checkField = values?.forecasted_end_date
      } else {
        checkField = status?.forecasted_end_date
      }

      if (checkField && Number(actualProgress) < 100) {
        if (!checkField) return true
        const isPastDate = !isAfter(this.parseDate(checkField), new Date())
        return isPastDate
      }
    }

    // Check if the stage is in 'Initiation, Design, or LDC Procurement' status
    if (this.isInitiationDesignLdc.includes(stage_status_name)) {
      const actualProgress = actualPercentage // Using actual plan percentage for relevant stages
      let checkField
      if ('forecast_finish' in values) {
        checkField = values?.forecast_finish
      } else {
        checkField = status?.forecast_finish
      }
      if (checkField && Number(actualProgress) < 100) {
        if (!checkField) return true
        const isPastDate = !isAfter(this.parseDate(checkField), new Date())
        return isPastDate
      }
    }

    return false
  }
}

export class ContractorProcurementValidation {
  // TODO: PROCUREMENT_START_DATE

  /**
   * Validates procurement start date.
   * - If actual progress is > 0, the procurement start date is required.
   * - Returns `true` if validation **fails** (i.e., missing or invalid date).
   * - Returns `false` if validation **passes**.
   */
  static async procurementDateValidation(values: any, status: IStatus): Promise<boolean> {
    let checkField
    // TODO: PROCUREMENT_START_DATE
    // if ('contract_start_date' in values) {
    //   checkField = values?.contract_start_date
    // } else {
    //   checkField = status?.contract_start_date
    // }

    if ('procurement_start_date' in values) {
      checkField = values?.procurement_start_date
    } else {
      checkField = status?.procurement_start_date
    }

    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status

    let actualProgress
    if ('actual_plan_percentage' in values) {
      actualProgress = values?.actual_plan_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_plan_percentage)
    }

    if (stage_status_name === CONTRACTOR_PROCUREMENT && Number(actualProgress) > 0) {
      if (!checkField) {
        return true
      }
    }
    return false
  }

  /**
   * Validates contractor field.
   * - If actual progress is 100, the contractor field is required.
   * - Returns `true` if validation **fails** (i.e., contractor is missing).
   * - Returns `false` if validation **passes**.
   */
  static async contractorFieldValidations(values: any, status: IStatus): Promise<boolean> {
    const checkField = values?.contractor ?? status?.MasterContractor?.id
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status
    const actualProgress = values?.actual_plan_percentage ?? convertToPercentage(status?.actual_plan_percentage)

    if (stage_status_name === CONTRACTOR_PROCUREMENT && Number(actualProgress) === 100) {
      if (!checkField) return true
    }
    return false
  }
}

export class LDCProcurementValidation {
  /**
   * Validates consultant field.
   * - If actual progress is 100, the consultant field is required.
   * - Returns `true` if validation **fails** (i.e., consultant is missing).
   * - Returns `false` if validation **passes**.
   */
  static async consultantFieldValidation(values: any, status: IStatus): Promise<boolean> {
    let checkField
    if ('consultant' in values) {
      checkField = values?.consultant
    } else {
      checkField = status?.MasterPMCConsultant?.id
    }
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status

    let actualProgress
    if ('actual_percentage' in values) {
      actualProgress = values?.actual_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_percentage)
    }
    if (stage_status_name === LDC_PROCUREMENT && Number(actualProgress) === 100) {
      if (!checkField) return true
    }
    return false
  }
}

export class DesignValidation {
  /**
   * Validates design manager field.
   * - If actual progress is greater then 100, the design manager field is required.
   * - Returns `true` if validation **fails** (i.e., design manager is missing).
   * - Returns `false` if validation **passes**.
   */
  static async designManagerFieldValidation(values: any, status: IStatus): Promise<boolean> {
    let checkField
    if ('design_manager' in values) {
      checkField = values?.design_manager
    } else {
      checkField = status?.DesignManagers
    }
    const stage_status = values?.stage_status ?? status?.stage_status
    let actualProgress
    if ('actual_percentage' in values) {
      actualProgress = values?.actual_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_percentage)
    }
    if (stage_status === DESIGN && Number(actualProgress) > 0) {
      if (!checkField) return true
    }
    return false
  }
}
export class SlippageJustificationValidation {
  /**
   * Validates Slippage Justification field.
    - Slippage Justification is mandatory when forecast_finish date is change and updated forecast_finish date is greater than forecast_completion_last_week for Initiation,LDC Procurement,Design,Contractor Procurement stage only
   */
  static async slippageJustificationFieldValidation(values: any, status: IStatus): Promise<boolean> {
    let checkField
    if (!status?.forecast_completion_last_week) return false
    if ('slippage_justification' in values) {
      checkField = values?.slippage_justification
    } else {
      checkField = status?.slippage_justification
    }
    const stage_status = values?.stage_status ?? status?.stage_status
    if (
      slippageJustificationValidation(
        stage_status,
        values?.forecast_finish || values?.forecasted_end_date,
        status?.forecast_completion_last_week || '',
      )
    ) {
      if (!checkField) return true
    }
    return false
  }
}

export class ConsultantDesignStageValidation {
  /**
   * Validates consultant field.
   * - If actual progress is greater then 100, the consultant field is required.
   * - Returns `true` if validation **fails** (i.e.,consultant is missing).
   * - Returns `false` if validation **passes**.
   */
  static async consultantFieldInDesignValidation(values: any, status: IStatus): Promise<boolean> {
    let checkField
    if ('consultant' in values) {
      checkField = values?.consultant
    } else {
      checkField = status?.Consultant
    }
    const stage_status = values?.stage_status ?? status?.stage_status
    let actualProgress
    if ('actual_percentage' in values) {
      actualProgress = values?.actual_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_percentage)
    }
    if (stage_status === DESIGN && Number(actualProgress) > 0) {
      if (!checkField) return true
    }
    return false
  }
}
export class ConstructionValidation {
  static async contractorRequire(values: any, status: IStatus): Promise<boolean> {
    let contractor
    if ('contractor' in values) {
      contractor = values?.contractor
    } else {
      contractor = status?.MasterContractor?.id
    }
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status

    let actualProgress
    if ('actual_plan_percentage' in values) {
      actualProgress = values?.actual_plan_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_plan_percentage)
    }
    if (stage_status_name === CONSTRUCTION && Number(actualProgress) > 0) {
      if (!contractor) return true
    }
    return false
  }

  static async contractStartDateRequire(values: any, status: IStatus): Promise<boolean> {
    let contract_start_date
    if ('contract_start_date' in values) {
      contract_start_date = values?.contract_start_date
    } else {
      contract_start_date = status?.contract_start_date
    }
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status

    let actualProgress
    if ('actual_plan_percentage' in values) {
      actualProgress = values?.actual_plan_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_plan_percentage)
    }
    if (stage_status_name === CONSTRUCTION && Number(actualProgress) > 0) {
      if (!contract_start_date) return true
    }
    return false
  }

  static async contractEndDateRequire(values: any, status: IStatus): Promise<boolean> {
    let contract_end_date
    if ('contract_end_date' in values) {
      contract_end_date = values?.contract_end_date
    } else {
      contract_end_date = status?.contract_end_date
    }
    const stage_status_name = values?.project_stage_status ?? status?.MasterProjectStageStatus?.project_stage_status
    let actualProgress
    if ('actual_plan_percentage' in values) {
      actualProgress = values?.actual_plan_percentage
    } else {
      actualProgress = convertToPercentage(status?.actual_plan_percentage)
    }
    if (stage_status_name === CONSTRUCTION && Number(actualProgress) > 0) {
      if (!contract_end_date) return true
    }
    return false
  }
}

// Validate whether the baseline plan finish date of the current status occurs after its preceding status date.
export class BaselinePlanFinishValidator {
  private static CONTRACTOR_CONSTRUCTION_DLP = [CONTRACTOR_PROCUREMENT, CONSTRUCTION, DLP_PROJECT_CLOSEOUT]
  private static INITIATION_LDC_DESIGN = [LDC_PROCUREMENT, DESIGN, INITIATION]

  /**
   * Parses a date string into a Date object.
   * Supports 'yyyy-MM-dd' and 'dd-MM-yyyy' formats.
   */
  private static parseDate(dateStr: string): Date | null {
    if (!dateStr) return null

    const yyyyMMddRegex = /^\d{4}-\d{2}-\d{2}$/
    return yyyyMMddRegex.test(dateStr)
      ? parse(dateStr, 'yyyy-MM-dd', new Date())
      : parse(dateStr, 'dd-MM-yyyy', new Date())
  }

  /**
   * Extracts the baseline plan date from either `values` or `status` object.
   */
  private static getBaselinePlanDate(
    status: IStatus,
    values: any,
    key: 'baseline_plan_finish' | 'plan_end_date',
  ): string | undefined {
    const value = key in values ? values[key] : status?.[key]
    if (typeof value === 'string' || typeof value === 'undefined') {
      return value
    }
    if (value instanceof Date) {
      return value.toISOString().split('T')[0] // format as 'yyyy-MM-dd'
    }
    return undefined
  }

  /**
   * Retrieves the last relevant status based on the stage type.
   */
  private static getPreviousStatus(status: IStatus, statuses: IStatus[]): IStatus | undefined {
    const statusHistory: IStatus[] = []

    for (const iterateStatus of statuses) {
      if (Number(status?.project_status_sorting_order) === Number(iterateStatus?.project_status_sorting_order)) {
        break
      }
      statusHistory.push(iterateStatus)
    }

    return statusHistory.length > 0 ? statusHistory[statusHistory.length - 1] : undefined
  }

  /**
   * Determines if the baseline plan finish date is valid based on project status and rules.
   */
  static async isValidBasicPlanFinishDate(status: IStatus, statuses: IStatus[], values: any): Promise<boolean> {
    const stageStatus = status?.MasterProjectStageStatus?.project_stage_status
    if (!stageStatus) return false

    const isInitiationLdcDesign = this.INITIATION_LDC_DESIGN.includes(stageStatus as string)
    const isContractorDesignConstruction = this.CONTRACTOR_CONSTRUCTION_DLP.includes(stageStatus as string)

    if (!isInitiationLdcDesign && !isContractorDesignConstruction) return false

    // Determine the baseline plan date based on the phase type
    const currentPlanDate: any = this.getBaselinePlanDate(
      status,
      values,
      isInitiationLdcDesign ? 'baseline_plan_finish' : 'plan_end_date',
    )

    if (!currentPlanDate) return false

    // Get the most recent previous status
    const previousStatus = this.getPreviousStatus(status, statuses)
    if (!previousStatus) return false

    // Determine the check date based on the last relevant status stage
    const referenceDate: any = this.INITIATION_LDC_DESIGN.includes(
      previousStatus.MasterProjectStageStatus?.project_stage_status as string,
    )
      ? previousStatus.baseline_plan_finish
      : previousStatus.plan_end_date

    if (!referenceDate) return false

    // Validate if baselinePlanDate is after checkDate
    const parsedBaselineDate = this.parseDate(currentPlanDate)
    const parsedCheckDate = this.parseDate(referenceDate)

    if (!parsedBaselineDate || !parsedCheckDate) return false // Ensure both are valid dates

    return !isAfter(parsedBaselineDate, parsedCheckDate)
  }

  /**
   * Determines if the baseline plan finish date should be greater than the plan start date.
   */
  static async isBaselinePlanFinishBeforePlanStartDate(
    status: IStatus,
    statuses: IStatus[],
    values: any,
  ): Promise<boolean> {
    if ('baseline_plan_finish' in values) {
      const planStartDate = status.plan_start_date
      if (planStartDate) {
        return !isDateBefore(planStartDate, values?.baseline_plan_finish)
      } else {
        return false
      }
    } else {
      return false
    }
  }
}

export class ForecastFinishValidator {
  /**
   * Determines if the baseline plan finish date should be greater than the plan start date.
   */
  static async isForecastStartBeforeForecastFinish(
    status: IStatus,
    statuses: IStatus[],
    values: any,
  ): Promise<boolean> {
    if ('forecasted_end_date' in values || 'forecast_finish' in values) {
      const forecastStartDate = status.forecast_start_date
      const endData = 'forecasted_end_date' in values ? values?.forecasted_end_date : values.forecast_finish
      if (forecastStartDate) {
        return !isDateBefore(forecastStartDate, endData)
      } else {
        return false
      }
    } else {
      return false
    }
  }
}

const STAGE_CONFIGURATIONS: Record<string, StageConfig> = {
  [CONSTRUCTION]: {
    fieldsToCheck: ['supervision_consultant', 'PMC_Consultant', 'contractor'],
    comparisonStage: CONTRACTOR_PROCUREMENT,
    messageTemplate: `The assigned {fields} is different from the ones in the ${CONTRACTOR_PROCUREMENT} stage. Do you want to proceed?`,
  },
  [DESIGN]: {
    fieldsToCheck: ['consultant'],
    comparisonStage: LDC_PROCUREMENT,
    messageTemplate: `The assigned {fields} is different from the ones in the ${LDC_PROCUREMENT} stage. Do you want to proceed?`,
  },
}

const checkStageConfirmation = (
  values: any,
  status: IStatus,
  statuses: IStatus[],
  stageConfig: StageConfig,
): string => {
  const { fieldsToCheck, comparisonStage, messageTemplate } = stageConfig

  // Check if comparison stage exists for same phase
  const comparisonStageExists = statuses?.find(
    (res) => res?.phase === status?.phase && res?.stage_status === comparisonStage,
  )

  if (!comparisonStageExists) return ''

  // Get matching fields that exist in values
  const matchingFields = fieldsToCheck?.filter((field) => field in values)
  if (matchingFields.length === 0) return ''

  // Format field names for message
  const fieldNames = matchingFields?.join(' and ').replace(/_/g, ' ')

  return messageTemplate?.replace('{fields}', fieldNames)
}

export const confirmationStatus = async (values: any, status: IStatus, statuses: IStatus[]) => {
  const currentStage = status?.stage_status as string
  const stageConfig = STAGE_CONFIGURATIONS[currentStage]

  if (!stageConfig) return ''

  return checkStageConfirmation(values, status, statuses, stageConfig)
}

// export const confirmationStatus1 = async (values: any, status: IStatus, statuses: IStatus[]) => {
//   let message = ''
//   if (status?.stage_status === CONSTRUCTION) {
//     const fieldsToCheck = ['supervision_consultant', 'PMC_Consultant', 'contractor']
//     // Note: Check for Contractor Procurement stage is exist for same phase
//     const isContractorProcurementExist = statuses?.find(
//       (res) => res?.phase === status?.phase && res?.stage_status === CONTRACTOR_PROCUREMENT,
//     )
//     console.log('isContractorProcurementExist: ', isContractorProcurementExist)
//     if (isContractorProcurementExist) {
//       const matchingFields = fieldsToCheck?.filter((field) => field in values)
//       const fieldNames = matchingFields?.join(' and ').replace(/_/g, ' ')
//       message =
//         matchingFields?.length > 0
//           ? `The assigned ${fieldNames} is different from the ones in the ${CONTRACTOR_PROCUREMENT} stage. Do you want to proceed?`
//           : ''
//     }
//   }
//   if (status?.stage_status === DESIGN) {
//     const fieldsToCheck = ['consultant']
//     // Note: Check for LDC Procurement stage is exist for same phase
//     const isLDCProcurementExist: IStatus | undefined = statuses?.find(
//       (res) => res?.phase === status?.phase && res?.stage_status === LDC_PROCUREMENT,
//     )
//     if (isLDCProcurementExist) {
//       const [key, value] = Object.entries(values)[0]
//       // Note: update value of design stage is same as LDC Procurement stage
//       const isSameValue =
//         Object.prototype.hasOwnProperty.call(isLDCProcurementExist, key) &&
//         isLDCProcurementExist[key as keyof IStatus] === value

//       const matchingFields = fieldsToCheck?.filter((field) => field in values)
//       const fieldNames = matchingFields?.join(' and ').replace(/_/g, ' ')
//       message =
//         matchingFields?.length > 0
//           ? `The assigned ${fieldNames} is different from the ones in the ${LDC_PROCUREMENT} stage. Do you want to proceed?`
//           : ''
//     }
//   }
//   return message
// }

//TODO: successor validation
// export const isSuccessorEndDateInvalid = (current: IStatus, allStatuses: IStatus[], values: any): string | null => {
//   const currentFinish = values?.forecast_finish || values?.baseline_plan_finish
//   if (!currentFinish) return null

//   // If no successors provided, skip
//   if (!current.successor || !Array.isArray(current.successor) || current.successor.length === 0) return null

//   // Match successor objects from full list
//   const successors = allStatuses.filter((item) => {
//     const successorKey =
//       (item.project_phase_category || '') +
//       (item.phase ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.phase}` : '') +
//       (item.stage_status ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.stage_status}` : '') +
//       (item.sub_stage ? `${PREDECESSOR_SUCCESSOR_SEPARATOR}${item.sub_stage}` : '')

//     return current?.successor?.includes(successorKey)
//   })

//   // Check if any successor ends BEFORE current phase
//   const invalid = successors.find((suc) => {
//     const sucFinish = suc.forecast_finish || suc.baseline_plan_finish
//     return sucFinish && isDateBefore(sucFinish, currentFinish) // error if successor ends before current
//   })

//   return invalid ? 'Invalid: The successor end date is before the current phase finish date.' : null
// }

export const isSuccessorEndDateInvalid = (current: IStatus, allStatuses: IStatus[], values: any): string | null => {
  const currentFinish = values?.forecasted_end_date || values?.forecast_finish
  if (!currentFinish) return null

  // New format successor array (with IDs)
  const successors = current.LookupProjectStatusSuccessor || []

  if (!Array.isArray(successors) || successors.length === 0) return null

  // Extract destination IDs
  const destinationIds = successors
    .map((suc: any) => suc?.DestinationProjectStatus?.destination_project_status_id)
    .filter((id): id is number => !!id)

  if (destinationIds.length === 0) return null

  // Match from allStatuses by ID
  const matchedSuccessors = allStatuses.filter((status: IStatus) => destinationIds.includes(status.id ?? 0))

  // Extract valid finish dates
  const validFinishDates = matchedSuccessors
    .map((suc) => suc.forecasted_end_date || suc.forecast_finish)
    .filter((date): date is string => isValidYMDDate(date ?? null))

  if (validFinishDates.length === 0) return null

  // Find the earliest finish date among all successors
  const minSuccessorFinish = validFinishDates.reduce((min, date) => (isDateBefore(date, min) ? date : min))

  // Check if current finish is after successor
  if (!isDateBefore(currentFinish, minSuccessorFinish)) {
    return 'The current stage finish date must not be after any successor’s finish date.'
  }

  return null // ✅ Valid
}

export const validateStatus = async (values: any, status: IStatus, statuses: IStatus[]) => {
  const messages = []

  /**
   * Determines if the baseline plan finish date is valid based on project status and rules.
   */
  const isNotValidBaseLinePlanDate = await BaselinePlanFinishValidator.isValidBasicPlanFinishDate(
    status,
    statuses,
    values,
  )

  const isBaselinePlanFinishBeforePlanStartDate =
    await BaselinePlanFinishValidator.isBaselinePlanFinishBeforePlanStartDate(status, statuses, values)

  const isForecastEndBeforeForeCastStart = await ForecastFinishValidator.isForecastStartBeforeForecastFinish(
    status,
    statuses,
    values,
  )

  //Actual Progress Validation
  // Validation for Forecasted Date being today or in the past
  const isNotAllowedPastDateOrTodayDateIfValueIsLess100 =
    await ActualProgressValidation.isNotAllowedPastDateOrTodayDateIfValueIsLess100(values, status as IStatus)
  const isMaxDateTodayIfValue100 = await ActualProgressValidation.isMaxDateTodayIfValue100(values, status as IStatus)

  // Contractor procurement Validation
  // Validation for Procurement Start Date being mandatory if Actual Progress is greater than 0
  const isProcurementDateValidate = await ContractorProcurementValidation.procurementDateValidation(
    values,
    status as IStatus,
  )

  //If actual progress is 100%, the contractor field must be filled.
  const isContractorFieldValidate = await ContractorProcurementValidation.contractorFieldValidations(
    values,
    status as IStatus,
  )

  //LDCProcurement Validation
  //If actual progress is 100%, the consultant field must be filled.
  const isConsultantFieldValidate = await LDCProcurementValidation.consultantFieldValidation(values, status as IStatus)
  // Construction Validation
  const isContractorFieldRequire = await ConstructionValidation.contractorRequire(values, status as IStatus)
  const isContractStartDateRequire = await ConstructionValidation.contractStartDateRequire(values, status as IStatus)
  const isContractEndDateRequire = await ConstructionValidation.contractEndDateRequire(values, status as IStatus)

  // Design manager in design stage Validation
  const isDesignManagerValidate = await DesignValidation.designManagerFieldValidation(values, status as IStatus)

  // Consultant in design stage Validation
  const isConsultantInDesignStageValidate = await ConsultantDesignStageValidation.consultantFieldInDesignValidation(
    values,
    status as IStatus,
  )

  // Slippage Justification validation
  const isSlippageJustification = await SlippageJustificationValidation.slippageJustificationFieldValidation(
    values,
    status as IStatus,
  )

  if (isSlippageJustification) {
    messages.push('Please make sure the Slippage Justification field is filled.')
  }

  // if (isNotValidBaseLinePlanDate) {
  //   messages.push('The baseline plan finish date must be on or after the previous status date.')
  // }

  if (isBaselinePlanFinishBeforePlanStartDate) {
    messages.push('The Baseline Plan Finish Date must be after the Plan Start Date')
  }

  if (isForecastEndBeforeForeCastStart) {
    messages.push('The Forecast Finish must be after the Forecast start Date')
  }

  // Push messages for each failed validation check
  if (isNotAllowedPastDateOrTodayDateIfValueIsLess100) {
    messages.push(
      "Please make sure the Actual/Forecast Finish Date is greater than today's date, since the Actual Progress % is not 100%.",
    )
  }
  if (isMaxDateTodayIfValue100) {
    messages.push(
      'The Actual/Forecast Finish Date must be set to a future date if the Actual Progress % is below 100%. If it is 100%, the maximum allowable date is today.',
    )
  }
  if (isProcurementDateValidate) {
    messages.push('Please make sure the Procurement Start Date is mandatory, as Actual Progress % is greater than 0.')
  }
  if (isContractorFieldValidate) {
    messages.push('Please make sure the Contractor field is filled, as Actual Progress % is 100%.')
  }
  if (isConsultantFieldValidate) {
    messages.push('Please make sure the Consultant field is filled, as Actual Progress % is 100%.')
  }
  if (isDesignManagerValidate) {
    messages.push('Please make sure the Design manager field is filled, as Actual Progress % is greater than 0.')
  }
  if (isConsultantInDesignStageValidate) {
    messages.push('Please make sure the Consultant field is filled, as Actual Progress % is greater than 0.')
  }
  // Contractor field and date validation messages
  if (isContractorFieldRequire) {
    messages.push('Please make sure the Contractor field is filled, as Actual Progress % is greater than 0.')
  }
  if (isContractStartDateRequire) {
    messages.push('Please make sure the Contract Start Date is filled, as Actual Progress % is greater than 0.')
  }
  if (isContractEndDateRequire) {
    messages.push('Please make sure the Contract End Date is filled, as Actual Progress % is greater than 0.')
  }

  // Check for successor end date validation
  // This checks if the successor end date is valid based on the current status and its successors
  // If the current status has successors, it will validate the end date against them
  // If the end date is invalid, it will return a message indicating the issue
  const isInvalidSuccessorEndDate = isSuccessorEndDateInvalid(status, statuses, values)

  if (isInvalidSuccessorEndDate) {
    messages.push(isInvalidSuccessorEndDate)
  }

  return messages
}

//this is comment code for all record check baselinePlanFinish
// for (let i = 0; i > statusData?.length; i++) {
//   const iterateStageStatus = statusData[i]?.stage_status
//   const nextStageStatus = statusData[i + 1]?.stage_status
//   let iterateBaseLine
//   let nextBaseLine
//   if (this.isContractDlpConstruction.includes(iterateStageStatus as string)) {
//     iterateBaseLine = statusData[i]?.baseline_plan_finish
//   } else if (this.isInitiationDesignLdc.includes(iterateStageStatus as string)) {
//     iterateBaseLine = statusData[i]?.baseline_plan_finish
//   }
//   if (this.isContractDlpConstruction.includes(nextStageStatus as string)) {
//     nextBaseLine = statusData[i + 1]?.plan_end_date
//   } else if (this.isInitiationDesignLdc.includes(iterateStageStatus as string)) {
//     nextBaseLine = statusData[i + 1]?.plan_end_date
//   }
//   if (
//     Number(status?.project_status_sorting_order) + 1 ===
//     Number(statusData[i]?.project_status_sorting_order) + 1
//   ) {
//     iterateBaseLine = baselinePlanDate
//   }
//   if (
//     Number(status?.project_status_sorting_order) + 1 ===
//     Number(statusData[i + 1]?.project_status_sorting_order) + 1
//   ) {
//     nextBaseLine = baselinePlanDate
//   }
//   const isAfterDate = isAfter(this.parseDate(iterateBaseLine), this.parseDate(nextBaseLine))
//   return isAfterDate
// }
